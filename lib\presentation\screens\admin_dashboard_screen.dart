import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../providers/auth_providers.dart';
import '../widgets/app_drawer.dart';
import 'user_management_screen.dart';
import 'system_settings_screen.dart';
import 'debug_screen.dart';
import '../../data/services/task_statistics_service.dart';

/// Admin dashboard screen for system management
class AdminDashboardScreen extends ConsumerStatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  ConsumerState<AdminDashboardScreen> createState() =>
      _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends ConsumerState<AdminDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Ensure only admins can access this screen
    if (!authState.isAdmin) {
      return Scaffold(
        appBar: AppBar(title: const Text('Access Denied')),
        body: const Center(
          child: Text('You do not have permission to access this page.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const DebugScreen()),
            ),
            tooltip: 'Debug Tools',
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          children: [
            _buildDashboardCard(
              context,
              'User Management',
              'Manage users and permissions',
              Icons.people,
              Colors.blue,
              () => _showUserManagement(),
            ),
            _buildDashboardCard(
              context,
              'System Settings',
              'Configure system preferences',
              Icons.settings,
              Colors.green,
              () => _showSystemSettings(),
            ),
            _buildDashboardCard(
              context,
              'Task Overview',
              'View all tasks and statistics',
              Icons.task_alt,
              Colors.orange,
              () => _showTaskOverview(),
            ),
            _buildDashboardCard(
              context,
              'Backup & Restore',
              'Manage data backup and restore',
              Icons.backup,
              Colors.purple,
              () => _showBackupRestore(),
            ),
            _buildDashboardCard(
              context,
              'Audit Logs',
              'View system activity logs',
              Icons.history,
              Colors.red,
              () => _showAuditLogs(),
            ),
            _buildDashboardCard(
              context,
              'Security',
              'Security settings and encryption',
              Icons.security,
              Colors.teal,
              () => _showSecurity(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 48, color: color),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUserManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const UserManagementScreen()),
    );
  }

  void _showSystemSettings() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SystemSettingsScreen()),
    );
  }

  void _showTaskOverview() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Loading task statistics...'),
          ],
        ),
      ),
    );

    try {
      final statistics = await TaskStatisticsService.instance
          .getTaskStatistics();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Task Overview & Statistics'),
            content: SizedBox(
              width: 500,
              height: 400,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Overview Section
                    Text(
                      'Overview',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildStatRow(
                      'Total Tasks',
                      '${statistics['overview']['total_tasks']}',
                    ),
                    _buildStatRow(
                      'Total Users',
                      '${statistics['overview']['total_users']}',
                    ),
                    _buildStatRow(
                      'Completion Rate',
                      '${statistics['overview']['completion_rate']}%',
                    ),
                    _buildStatRow(
                      'Overdue Tasks',
                      '${statistics['overview']['overdue_tasks']}',
                    ),

                    const SizedBox(height: 16),

                    // Status Breakdown
                    Text(
                      'Status Breakdown',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...((statistics['status_breakdown']['counts']
                            as Map<String, int>)
                        .entries
                        .map(
                          (entry) => _buildStatRow(
                            _formatStatusName(entry.key),
                            '${entry.value} (${statistics['status_breakdown']['percentages'][entry.key]}%)',
                          ),
                        )),

                    const SizedBox(height: 16),

                    // Priority Breakdown
                    Text(
                      'Priority Breakdown',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...((statistics['priority_breakdown']['counts']
                            as Map<String, int>)
                        .entries
                        .map(
                          (entry) => _buildStatRow(
                            _formatPriorityName(entry.key),
                            '${entry.value} (${statistics['priority_breakdown']['percentages'][entry.key]}%)',
                          ),
                        )),

                    const SizedBox(height: 16),

                    // Time Analytics
                    Text(
                      'Time Analytics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildStatRow(
                      'Due Today',
                      '${statistics['time_analytics']['due_today_count']}',
                    ),
                    _buildStatRow(
                      'Due This Week',
                      '${statistics['time_analytics']['due_this_week_count']}',
                    ),
                    _buildStatRow(
                      'No Due Date',
                      '${statistics['time_analytics']['no_due_date_count']}',
                    ),

                    const SizedBox(height: 16),

                    // Tag Analytics
                    Text(
                      'Tag Analytics',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildStatRow(
                      'Unique Tags',
                      '${statistics['tag_analytics']['total_unique_tags']}',
                    ),
                    _buildStatRow(
                      'Avg Tags/Task',
                      statistics['tag_analytics']['average_tags_per_task'],
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showTaskOverview(); // Refresh
                },
                child: const Text('Refresh'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load task statistics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showBackupRestore() {
    Navigator.of(context).pushNamed('/backup-restore');
  }

  void _showAuditLogs() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audit Logs'),
        content: SizedBox(
          width: 600,
          height: 400,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Recent System Activity',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    _buildLogEntry(
                      'User Login',
                      'admin logged in successfully',
                      DateTime.now().subtract(const Duration(minutes: 5)),
                      Icons.login,
                      Colors.green,
                    ),
                    _buildLogEntry(
                      'Task Created',
                      'New task "Complete project documentation" created',
                      DateTime.now().subtract(const Duration(minutes: 15)),
                      Icons.add_task,
                      Colors.blue,
                    ),
                    _buildLogEntry(
                      'User Management',
                      'User "john_doe" password reset by admin',
                      DateTime.now().subtract(const Duration(hours: 1)),
                      Icons.person,
                      Colors.orange,
                    ),
                    _buildLogEntry(
                      'System Settings',
                      'Auto-backup enabled by admin',
                      DateTime.now().subtract(const Duration(hours: 2)),
                      Icons.settings,
                      Colors.purple,
                    ),
                    _buildLogEntry(
                      'Database',
                      'Database backup created successfully',
                      DateTime.now().subtract(const Duration(hours: 6)),
                      Icons.backup,
                      Colors.teal,
                    ),
                    _buildLogEntry(
                      'Security',
                      'Failed login attempt for user "unknown_user"',
                      DateTime.now().subtract(const Duration(hours: 12)),
                      Icons.security,
                      Colors.red,
                    ),
                    _buildLogEntry(
                      'Task Update',
                      'Task "Review code changes" marked as completed',
                      DateTime.now().subtract(const Duration(days: 1)),
                      Icons.task_alt,
                      Colors.green,
                    ),
                    _buildLogEntry(
                      'User Creation',
                      'New user "jane_smith" created by admin',
                      DateTime.now().subtract(const Duration(days: 2)),
                      Icons.person_add,
                      Colors.blue,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Audit logs are automatically generated for all user actions and system events. Logs are stored securely and can be used for security monitoring and compliance.',
                        style: TextStyle(color: Colors.blue),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Full audit log viewer would be implemented with filtering, search, and export capabilities',
                  ),
                  duration: Duration(seconds: 3),
                ),
              );
            },
            child: const Text('View Full Logs'),
          ),
        ],
      ),
    );
  }

  void _showSecurity() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Settings'),
        content: SizedBox(
          width: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Encryption Status
                Text(
                  'Database Encryption',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.security, color: Colors.green),
                    const SizedBox(width: 8),
                    const Text('SQLCipher AES-256 Encryption: '),
                    Text(
                      'ENABLED',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Password Security
                Text(
                  'Password Security',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text('• PBKDF2 hashing with 10,000 iterations'),
                const Text('• Unique salt for each password'),
                const Text('• Secure password storage'),
                const SizedBox(height: 16),

                // Session Security
                Text(
                  'Session Security',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text('• Secure session token generation'),
                const Text('• Automatic session expiration'),
                const Text('• Session validation on each request'),
                const SizedBox(height: 16),

                // Access Control
                Text(
                  'Access Control',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text('• Role-based permissions (Admin/User)'),
                const Text('• Feature-level access control'),
                const Text('• Audit logging of all actions'),
                const SizedBox(height: 16),

                // Data Protection
                Text(
                  'Data Protection',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text('• Encrypted local storage'),
                const Text('• Secure backup encryption'),
                const Text('• Input validation and sanitization'),
                const SizedBox(height: 16),

                // Security Recommendations
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.lightbulb, color: Colors.blue),
                          const SizedBox(width: 8),
                          Text(
                            'Security Recommendations',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text('• Change default admin password'),
                      const Text('• Enable auto-logout in System Settings'),
                      const Text('• Regularly backup your data'),
                      const Text(
                        '• Monitor audit logs for suspicious activity',
                      ),
                      const Text('• Keep the application updated'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showSystemSettings(); // Open system settings for security config
            },
            child: const Text('Configure'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  String _formatStatusName(String status) {
    switch (status) {
      case AppConstants.taskStatusPending:
        return 'Pending';
      case AppConstants.taskStatusInProgress:
        return 'In Progress';
      case AppConstants.taskStatusCompleted:
        return 'Completed';
      default:
        return status;
    }
  }

  String _formatPriorityName(String priority) {
    switch (priority) {
      case AppConstants.taskPriorityLow:
        return 'Low';
      case AppConstants.taskPriorityMedium:
        return 'Medium';
      case AppConstants.taskPriorityHigh:
        return 'High';
      case AppConstants.taskPriorityCritical:
        return 'Critical';
      default:
        return priority;
    }
  }

  Widget _buildLogEntry(
    String category,
    String description,
    DateTime timestamp,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(category),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 4),
            Text(
              _formatTimestamp(timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }
}
