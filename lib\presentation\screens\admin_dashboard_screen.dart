import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../providers/auth_providers.dart';
import '../widgets/app_drawer.dart';
import 'user_management_screen.dart';

/// Admin dashboard screen for system management
class AdminDashboardScreen extends ConsumerStatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  ConsumerState<AdminDashboardScreen> createState() =>
      _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends ConsumerState<AdminDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Ensure only admins can access this screen
    if (!authState.isAdmin) {
      return Scaffold(
        appBar: AppBar(title: const Text('Access Denied')),
        body: const Center(
          child: Text('You do not have permission to access this page.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(title: const Text('Admin Dashboard')),
      drawer: const AppDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          children: [
            _buildDashboardCard(
              context,
              'User Management',
              'Manage users and permissions',
              Icons.people,
              Colors.blue,
              () => _showUserManagement(),
            ),
            _buildDashboardCard(
              context,
              'System Settings',
              'Configure system preferences',
              Icons.settings,
              Colors.green,
              () => _showSystemSettings(),
            ),
            _buildDashboardCard(
              context,
              'Task Overview',
              'View all tasks and statistics',
              Icons.task_alt,
              Colors.orange,
              () => _showTaskOverview(),
            ),
            _buildDashboardCard(
              context,
              'Backup & Restore',
              'Manage data backup and restore',
              Icons.backup,
              Colors.purple,
              () => _showBackupRestore(),
            ),
            _buildDashboardCard(
              context,
              'Audit Logs',
              'View system activity logs',
              Icons.history,
              Colors.red,
              () => _showAuditLogs(),
            ),
            _buildDashboardCard(
              context,
              'Security',
              'Security settings and encryption',
              Icons.security,
              Colors.teal,
              () => _showSecurity(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 48, color: color),
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUserManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const UserManagementScreen()),
    );
  }

  void _showSystemSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('System Settings'),
        content: const Text(
          'System settings will be implemented in the next phase.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showTaskOverview() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Task Overview'),
        content: const Text(
          'Task overview and statistics will be implemented in the next phase.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showBackupRestore() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup & Restore'),
        content: const Text(
          'Backup and restore functionality will be implemented in the next phase.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAuditLogs() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Audit Logs'),
        content: const Text(
          'Audit logs viewer will be implemented in the next phase.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSecurity() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Settings'),
        content: const Text(
          'Security settings will be implemented in the next phase.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
