import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/design_system/app_design_system.dart';
import '../../core/models/task.dart';
import '../providers/task_providers.dart';
import 'modern_components.dart';

/// Modern, professional task creation/editing modal
class ModernAddTaskModal extends ConsumerStatefulWidget {
  final Task? task;
  final bool isEditing;

  const ModernAddTaskModal({super.key, this.task}) : isEditing = task != null;

  @override
  ConsumerState<ModernAddTaskModal> createState() => _ModernAddTaskModalState();
}

class _ModernAddTaskModalState extends ConsumerState<ModernAddTaskModal>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _tagsController;

  String _selectedStatus = AppConstants.taskStatusPending;
  String _selectedPriority = AppConstants.taskPriorityMedium;
  String _selectedCategory = AppConstants.taskCategoryGeneral;
  DateTime? _selectedDueDate;
  TimeOfDay? _selectedDueTime;
  bool _isShared = false;
  List<String> _tags = [];
  bool _isLoading = false;

  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    _tagsController = TextEditingController();

    if (widget.isEditing && widget.task != null) {
      _initializeWithTask(widget.task!);
    }
  }

  void _initializeWithTask(Task task) {
    _titleController.text = task.title;
    _descriptionController.text = task.description ?? '';
    _selectedStatus = task.status;
    _selectedPriority = task.priority;
    _selectedCategory = task.category;
    _selectedDueDate = task.dueDate;
    _selectedDueTime = task.dueDate != null
        ? TimeOfDay.fromDateTime(task.dueDate!)
        : null;
    _isShared = task.isShared;
    _tags = List.from(task.tags);
    _tagsController.text = _tags.join(', ');
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    final borderRadius = AppDesignSystem.borderRadius;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(spacing.lg),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(borderRadius.large),
          boxShadow: AppDesignSystem.shadows.large,
        ),
        child: Column(
          children: [
            _buildHeader(theme, spacing),
            _buildTabBar(theme),
            Expanded(child: _buildTabContent(spacing)),
            _buildFooter(theme, spacing),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, dynamic spacing) {
    return Container(
      padding: EdgeInsets.all(spacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDesignSystem.borderRadius.large),
          topRight: Radius.circular(AppDesignSystem.borderRadius.large),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(spacing.sm),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(
                AppDesignSystem.borderRadius.small,
              ),
            ),
            child: Icon(
              widget.isEditing ? Icons.edit_note : Icons.add_task,
              color: theme.colorScheme.onPrimaryContainer,
              size: 24,
            ),
          ),
          SizedBox(width: spacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.isEditing ? 'Edit Task' : 'Create New Task',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  widget.isEditing
                      ? 'Update task details and settings'
                      : 'Add a new task to your workflow',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.surface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.info_outline), text: 'Basic Info'),
          Tab(icon: Icon(Icons.tune), text: 'Properties'),
          Tab(icon: Icon(Icons.schedule), text: 'Schedule'),
        ],
      ),
    );
  }

  Widget _buildTabContent(dynamic spacing) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildBasicInfoTab(spacing),
        _buildPropertiesTab(spacing),
        _buildScheduleTab(spacing),
      ],
    );
  }

  Widget _buildBasicInfoTab(dynamic spacing) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(spacing.lg),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ModernInputField(
              label: 'Task Title *',
              hint: 'Enter a clear, descriptive title',
              controller: _titleController,
              prefixIcon: Icons.title,
            ),
            SizedBox(height: spacing.lg),
            ModernInputField(
              label: 'Description',
              hint: 'Add detailed description, requirements, or notes',
              controller: _descriptionController,
              prefixIcon: Icons.description,
              maxLines: 4,
            ),
            SizedBox(height: spacing.lg),
            ModernInputField(
              label: 'Tags',
              hint:
                  'Enter tags separated by commas (e.g., urgent, frontend, bug)',
              controller: _tagsController,
              prefixIcon: Icons.local_offer,
              onChanged: _onTagsChanged,
            ),
            if (_tags.isNotEmpty) ...[
              SizedBox(height: spacing.md),
              Wrap(
                spacing: spacing.sm,
                runSpacing: spacing.sm,
                children: _tags
                    .map(
                      (tag) => ModernChip(
                        label: tag,
                        selected: true,
                        icon: Icons.tag,
                      ),
                    )
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPropertiesTab(dynamic spacing) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(spacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPropertySection(
            'Status',
            'Current state of the task',
            Icons.flag,
            _buildStatusSelector(theme, spacing),
            theme,
            spacing,
          ),
          SizedBox(height: spacing.xl),
          _buildPropertySection(
            'Priority',
            'Task importance level',
            Icons.priority_high,
            _buildPrioritySelector(theme, spacing),
            theme,
            spacing,
          ),
          SizedBox(height: spacing.xl),
          _buildPropertySection(
            'Category',
            'Task classification',
            Icons.category,
            _buildCategorySelector(theme, spacing),
            theme,
            spacing,
          ),
          SizedBox(height: spacing.xl),
          _buildPropertySection(
            'Sharing',
            'Task visibility settings',
            Icons.share,
            _buildSharingToggle(theme, spacing),
            theme,
            spacing,
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleTab(dynamic spacing) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(spacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPropertySection(
            'Due Date',
            'When should this task be completed?',
            Icons.calendar_today,
            _buildDateSelector(theme, spacing),
            theme,
            spacing,
          ),
          if (_selectedDueDate != null) ...[
            SizedBox(height: spacing.xl),
            _buildPropertySection(
              'Due Time',
              'Specific time for completion',
              Icons.access_time,
              _buildTimeSelector(theme, spacing),
              theme,
              spacing,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPropertySection(
    String title,
    String description,
    IconData icon,
    Widget content,
    ThemeData theme,
    dynamic spacing,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 20, color: theme.colorScheme.primary),
            SizedBox(width: spacing.sm),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: spacing.xs),
        Text(
          description,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: spacing.md),
        content,
      ],
    );
  }

  Widget _buildStatusSelector(ThemeData theme, dynamic spacing) {
    final statuses = [
      (AppConstants.taskStatusPending, 'Pending', Icons.schedule),
      (AppConstants.taskStatusInProgress, 'In Progress', Icons.play_circle),
      (AppConstants.taskStatusCompleted, 'Completed', Icons.check_circle),
    ];

    return Wrap(
      spacing: spacing.sm,
      runSpacing: spacing.sm,
      children: statuses
          .map(
            (status) => ModernChip(
              label: status.$2,
              icon: status.$3,
              selected: _selectedStatus == status.$1,
              onTap: () => setState(() => _selectedStatus = status.$1),
            ),
          )
          .toList(),
    );
  }

  Widget _buildPrioritySelector(ThemeData theme, dynamic spacing) {
    final priorities = [
      (AppConstants.taskPriorityLow, 'Low', AppDesignSystem.priorityColors.low),
      (
        AppConstants.taskPriorityMedium,
        'Medium',
        AppDesignSystem.priorityColors.medium,
      ),
      (
        AppConstants.taskPriorityHigh,
        'High',
        AppDesignSystem.priorityColors.high,
      ),
      (
        AppConstants.taskPriorityCritical,
        'Critical',
        AppDesignSystem.priorityColors.critical,
      ),
    ];

    return Wrap(
      spacing: spacing.sm,
      runSpacing: spacing.sm,
      children: priorities
          .map(
            (priority) => ModernChip(
              label: priority.$2,
              selected: _selectedPriority == priority.$1,
              backgroundColor: priority.$3.withValues(alpha: 0.1),
              textColor: priority.$3,
              onTap: () => setState(() => _selectedPriority = priority.$1),
            ),
          )
          .toList(),
    );
  }

  Widget _buildCategorySelector(ThemeData theme, dynamic spacing) {
    final categories = [
      (AppConstants.taskCategoryWork, 'Work', Icons.work),
      (AppConstants.taskCategoryPersonal, 'Personal', Icons.person),
      (AppConstants.taskCategoryProject, 'Project', Icons.folder),
      (AppConstants.taskCategoryMeeting, 'Meeting', Icons.meeting_room),
      (AppConstants.taskCategoryReminder, 'Reminder', Icons.notifications),
      (AppConstants.taskCategoryGeneral, 'General', Icons.task),
    ];

    return Wrap(
      spacing: spacing.sm,
      runSpacing: spacing.sm,
      children: categories
          .map(
            (category) => ModernChip(
              label: category.$2,
              icon: category.$3,
              selected: _selectedCategory == category.$1,
              onTap: () => setState(() => _selectedCategory = category.$1),
            ),
          )
          .toList(),
    );
  }

  Widget _buildSharingToggle(ThemeData theme, dynamic spacing) {
    return ModernCard(
      padding: EdgeInsets.all(spacing.md),
      elevated: false,
      child: Row(
        children: [
          Icon(
            _isShared ? Icons.public : Icons.lock,
            color: _isShared
                ? AppDesignSystem.statusColors.success
                : theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: spacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isShared ? 'Shared Task' : 'Private Task',
                  style: theme.textTheme.titleSmall,
                ),
                Text(
                  _isShared
                      ? 'Visible to all team members'
                      : 'Only visible to you',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isShared,
            onChanged: (value) => setState(() => _isShared = value),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector(ThemeData theme, dynamic spacing) {
    return ModernCard(
      padding: EdgeInsets.all(spacing.md),
      elevated: false,
      onTap: _selectDate,
      child: Row(
        children: [
          Icon(
            Icons.calendar_today,
            color: _selectedDueDate != null
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: spacing.md),
          Expanded(
            child: Text(
              _selectedDueDate != null
                  ? '${_selectedDueDate!.day}/${_selectedDueDate!.month}/${_selectedDueDate!.year}'
                  : 'No due date set',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: _selectedDueDate != null
                    ? theme.colorScheme.onSurface
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          if (_selectedDueDate != null)
            IconButton(
              onPressed: () => setState(() {
                _selectedDueDate = null;
                _selectedDueTime = null;
              }),
              icon: const Icon(Icons.clear),
              iconSize: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector(ThemeData theme, dynamic spacing) {
    return ModernCard(
      padding: EdgeInsets.all(spacing.md),
      elevated: false,
      onTap: _selectTime,
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: _selectedDueTime != null
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: spacing.md),
          Expanded(
            child: Text(
              _selectedDueTime != null
                  ? _selectedDueTime!.format(context)
                  : 'No time set',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: _selectedDueTime != null
                    ? theme.colorScheme.onSurface
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          if (_selectedDueTime != null)
            IconButton(
              onPressed: () => setState(() => _selectedDueTime = null),
              icon: const Icon(Icons.clear),
              iconSize: 20,
            ),
        ],
      ),
    );
  }

  Widget _buildFooter(ThemeData theme, dynamic spacing) {
    return Container(
      padding: EdgeInsets.all(spacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDesignSystem.borderRadius.large),
          bottomRight: Radius.circular(AppDesignSystem.borderRadius.large),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ModernButton(
              text: 'Cancel',
              variant: ModernButtonVariant.secondary,
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          SizedBox(width: spacing.md),
          Expanded(
            flex: 2,
            child: ModernButton(
              text: widget.isEditing ? 'Update Task' : 'Create Task',
              icon: widget.isEditing ? Icons.update : Icons.add,
              isLoading: _isLoading,
              onPressed: _saveTask,
            ),
          ),
        ],
      ),
    );
  }

  void _onTagsChanged(String value) {
    final tags = value
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();
    setState(() => _tags = tags);
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() => _selectedDueDate = date);
    }
  }

  Future<void> _selectTime() async {
    if (_selectedDueDate == null) return;

    final time = await showTimePicker(
      context: context,
      initialTime: _selectedDueTime ?? TimeOfDay.now(),
    );

    if (time != null) {
      setState(() => _selectedDueTime = time);
    }
  }

  Future<void> _saveTask() async {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a task title'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      DateTime? dueDateTime;
      if (_selectedDueDate != null) {
        dueDateTime = DateTime(
          _selectedDueDate!.year,
          _selectedDueDate!.month,
          _selectedDueDate!.day,
          _selectedDueTime?.hour ?? 23,
          _selectedDueTime?.minute ?? 59,
        );
      }

      bool success;
      if (widget.isEditing) {
        success = await ref
            .read(taskListProvider.notifier)
            .updateTask(
              id: widget.task!.id!,
              title: _titleController.text.trim(),
              description: _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
              status: _selectedStatus,
              priority: _selectedPriority,
              category: _selectedCategory,
              tags: _tags,
              dueDate: dueDateTime,
              isShared: _isShared,
            );
      } else {
        success = await ref
            .read(taskListProvider.notifier)
            .createTask(
              title: _titleController.text.trim(),
              description: _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
              status: _selectedStatus,
              priority: _selectedPriority,
              category: _selectedCategory,
              tags: _tags,
              dueDate: dueDateTime,
              isShared: _isShared,
            );
      }

      if (success && mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.isEditing
                  ? 'Task updated successfully'
                  : 'Task created successfully',
            ),
            backgroundColor: AppDesignSystem.statusColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save task: $e'),
            backgroundColor: AppDesignSystem.statusColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
