/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'AppException: $message';
}

/// Database related exceptions
class AppDatabaseException extends AppException {
  const AppDatabaseException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'AppDatabaseException: $message';
}

/// Authentication related exceptions
class AuthenticationException extends AppException {
  const AuthenticationException(
    super.message, {
    super.code,
    super.originalError,
  });

  @override
  String toString() => 'AuthenticationException: $message';
}

/// Authorization related exceptions
class AuthorizationException extends AppException {
  const AuthorizationException(
    super.message, {
    super.code,
    super.originalError,
  });

  @override
  String toString() => 'AuthorizationException: $message';
}

/// Validation related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'ValidationException: $message';
}

/// File operation related exceptions
class FileOperationException extends AppException {
  const FileOperationException(
    super.message, {
    super.code,
    super.originalError,
  });

  @override
  String toString() => 'FileOperationException: $message';
}

/// Encryption related exceptions
class EncryptionException extends AppException {
  const EncryptionException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'EncryptionException: $message';
}

/// Backup and restore related exceptions
class BackupException extends AppException {
  const BackupException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'BackupException: $message';
}

/// Network related exceptions (for future use)
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'NetworkException: $message';
}

/// Common exception messages
class ExceptionMessages {
  static const String invalidCredentials = 'Invalid username or password';
  static const String userNotFound = 'User not found';
  static const String userAlreadyExists = 'User already exists';
  static const String insufficientPermissions =
      'Insufficient permissions to perform this action';
  static const String taskNotFound = 'Task not found';
  static const String databaseConnectionFailed =
      'Failed to connect to database';
  static const String encryptionKeyNotFound = 'Encryption key not found';
  static const String invalidEncryptionKey = 'Invalid encryption key';
  static const String fileNotFound = 'File not found';
  static const String backupFailed = 'Backup operation failed';
  static const String restoreFailed = 'Restore operation failed';
  static const String invalidInput = 'Invalid input provided';
  static const String operationCancelled = 'Operation was cancelled';
  static const String unknownError = 'An unknown error occurred';
}
