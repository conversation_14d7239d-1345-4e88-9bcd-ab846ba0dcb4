import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/constants/app_constants.dart';

/// Comprehensive help and support screen
class HelpScreen extends StatefulWidget {
  const HelpScreen({super.key});

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.help_outline), text: 'Getting Started'),
            Tab(icon: Icon(Icons.book), text: 'User Guide'),
            Tab(icon: Icon(Icons.question_answer), text: 'FAQ'),
            Tab(icon: Icon(Icons.support_agent), text: 'Support'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGettingStartedTab(),
          _buildUserGuideTab(),
          _buildFAQTab(),
          _buildSupportTab(),
        ],
      ),
    );
  }

  Widget _buildGettingStartedTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            'Welcome to TaskManager Pro',
            'Your secure desktop task management solution',
            [
              'TaskManager Pro is a comprehensive task management application designed for security and productivity.',
              'Features include encrypted data storage, user management, advanced filtering, and backup capabilities.',
            ],
            Icons.rocket_launch,
            Colors.blue,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          _buildSectionCard(
            'First Time Setup',
            'Get started in 3 easy steps',
            [
              '1. Login with admin credentials (admin/admin123)',
              '2. Create additional users through Admin Dashboard',
              '3. Start creating and organizing your tasks',
            ],
            Icons.settings,
            Colors.green,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          _buildSectionCard(
            'Key Features',
            'What you can do with TaskManager Pro',
            [
              '• Create tasks with priorities, categories, and tags',
              '• Set due dates and track completion status',
              '• Filter and search tasks efficiently',
              '• Manage users and permissions (admin only)',
              '• Backup and restore your data securely',
              '• Switch between light and dark themes',
            ],
            Icons.star,
            Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildUserGuideTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGuideSection(
            'Managing Tasks',
            [
              'Creating Tasks: Click the + button to create a new task',
              'Task Properties: Set title, description, priority, category, and tags',
              'Due Dates: Add optional due dates with time',
              'Status Tracking: Move tasks between Pending, In Progress, and Completed',
              'Filtering: Use filter chips to find specific tasks',
              'Search: Use the search bar to find tasks by title or description',
            ],
          ),
          
          _buildGuideSection(
            'User Management (Admin Only)',
            [
              'Access: Open drawer → Admin Dashboard → User Management',
              'Creating Users: Click + to add new users with roles',
              'Editing Users: Click on any user to modify their details',
              'Password Reset: Use the menu to reset user passwords',
              'User Status: Activate or deactivate user accounts',
              'Statistics: View user counts and activity metrics',
            ],
          ),
          
          _buildGuideSection(
            'Backup & Restore',
            [
              'Creating Backups: Go to Backup & Restore → Create Backup',
              'Selective Backup: Choose to backup users, tasks, or both',
              'Viewing Backups: See all available backups with details',
              'Restoring Data: Select a backup and click Restore',
              'Managing Backups: Delete old backups to save space',
            ],
          ),
          
          _buildGuideSection(
            'Settings & Preferences',
            [
              'Theme: Switch between Light, Dark, and System themes',
              'Auto-Login: Enable automatic login for convenience',
              'Database Info: View storage usage and statistics',
              'Security: Manage password and session settings',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFAQTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          _buildFAQItem(
            'How do I reset my password?',
            'Only administrators can reset passwords. Contact your admin or use the admin account to reset user passwords through User Management.',
          ),
          
          _buildFAQItem(
            'Can I share tasks with other users?',
            'Yes! When creating or editing a task, enable the "Shared" option to make it visible to all users.',
          ),
          
          _buildFAQItem(
            'How secure is my data?',
            'Very secure! TaskManager Pro uses SQLCipher encryption for database storage, PBKDF2 password hashing, and secure session management.',
          ),
          
          _buildFAQItem(
            'Can I backup my data?',
            'Absolutely! Use the Backup & Restore feature to create secure backups of your tasks and user data.',
          ),
          
          _buildFAQItem(
            'What happens if I forget the admin password?',
            'The default admin credentials are username: "admin" and password: "admin123". Change these after first login for security.',
          ),
          
          _buildFAQItem(
            'How do I filter tasks?',
            'Use the filter chips above the task list to filter by status, priority, or category. You can also use the search bar for text-based filtering.',
          ),
          
          _buildFAQItem(
            'Can I change the app theme?',
            'Yes! Go to Settings → Appearance and choose between Light, Dark, or System theme modes.',
          ),
          
          _buildFAQItem(
            'How do I create categories and priorities?',
            'Categories and priorities are predefined for consistency. Available categories include Work, Personal, Project, Meeting, and Reminder. Priorities range from Low to Critical.',
          ),
        ],
      ),
    );
  }

  Widget _buildSupportTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            'Contact Support',
            'Get help when you need it',
            [
              'For technical support and bug reports, please contact our support team.',
              'Include your app version and a description of the issue.',
            ],
            Icons.support_agent,
            Colors.blue,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'System Information',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  _buildInfoRow('App Version', AppConstants.appVersion),
                  _buildInfoRow('App Name', AppConstants.appName),
                  _buildInfoRow('Database Version', AppConstants.databaseVersion.toString()),
                  
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _copySystemInfo,
                      icon: const Icon(Icons.copy),
                      label: const Text('Copy System Info'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          _buildSectionCard(
            'Troubleshooting',
            'Common solutions',
            [
              '• Restart the application if you experience issues',
              '• Check database statistics in Settings if tasks aren\'t loading',
              '• Ensure you have sufficient disk space for backups',
              '• Contact admin if you can\'t access certain features',
            ],
            Icons.build,
            Colors.orange,
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          _buildSectionCard(
            'Feature Requests',
            'Help us improve',
            [
              'We\'re always looking to improve TaskManager Pro.',
              'Send us your feature requests and suggestions.',
              'Your feedback helps us build better software.',
            ],
            Icons.lightbulb,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(String title, String subtitle, List<String> content, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...content.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(item),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildGuideSection(String title, List<String> items) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                  Expanded(child: Text(item)),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQItem(String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Text(answer),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _copySystemInfo() {
    final systemInfo = '''
App Name: ${AppConstants.appName}
App Version: ${AppConstants.appVersion}
Database Version: ${AppConstants.databaseVersion}
Platform: Desktop (Windows/Linux/macOS)
''';

    Clipboard.setData(ClipboardData(text: systemInfo));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('System information copied to clipboard'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
