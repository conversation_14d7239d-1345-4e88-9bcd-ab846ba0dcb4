import 'package:flutter/material.dart';

/// ERP Module Definitions
/// Comprehensive list of all ERP modules for enterprise resource planning
class ERPModules {
  // Private constructor
  ERPModules._();

  /// Core Modules
  static const String dashboard = 'dashboard';
  static const String analytics = 'analytics';
  static const String reports = 'reports';
  static const String settings = 'settings';

  /// Human Resources
  static const String hrDashboard = 'hr_dashboard';
  static const String employees = 'employees';
  static const String payroll = 'payroll';
  static const String attendance = 'attendance';
  static const String recruitment = 'recruitment';
  static const String performance = 'performance';
  static const String training = 'training';
  static const String benefits = 'benefits';
  static const String leaves = 'leaves';
  static const String expenses = 'expenses';

  /// Finance & Accounting
  static const String financeDashboard = 'finance_dashboard';
  static const String accounts = 'accounts';
  static const String invoicing = 'invoicing';
  static const String billing = 'billing';
  static const String payments = 'payments';
  static const String budgeting = 'budgeting';
  static const String taxation = 'taxation';
  static const String assets = 'assets';
  static const String banking = 'banking';
  static const String reconciliation = 'reconciliation';

  /// Sales & CRM
  static const String salesDashboard = 'sales_dashboard';
  static const String leads = 'leads';
  static const String opportunities = 'opportunities';
  static const String customers = 'customers';
  static const String contacts = 'contacts';
  static const String quotes = 'quotes';
  static const String orders = 'orders';
  static const String campaigns = 'campaigns';
  static const String pipeline = 'pipeline';
  static const String forecasting = 'forecasting';

  /// Inventory & Supply Chain
  static const String inventoryDashboard = 'inventory_dashboard';
  static const String products = 'products';
  static const String inventory = 'inventory';
  static const String warehouses = 'warehouses';
  static const String suppliers = 'suppliers';
  static const String purchasing = 'purchasing';
  static const String stockMovements = 'stock_movements';
  static const String qualityControl = 'quality_control';
  static const String logistics = 'logistics';
  static const String shipping = 'shipping';

  /// Manufacturing
  static const String manufacturingDashboard = 'manufacturing_dashboard';
  static const String production = 'production';
  static const String workOrders = 'work_orders';
  static const String bom = 'bill_of_materials';
  static const String routing = 'routing';
  static const String capacity = 'capacity_planning';
  static const String maintenance = 'maintenance';
  static const String shopFloor = 'shop_floor';
  static const String scheduling = 'production_scheduling';

  /// Project Management
  static const String projectDashboard = 'project_dashboard';
  static const String projects = 'projects';
  static const String tasks = 'tasks';
  static const String milestones = 'milestones';
  static const String timeTracking = 'time_tracking';
  static const String resources = 'resource_management';
  static const String gantt = 'gantt_charts';
  static const String collaboration = 'collaboration';
  static const String documents = 'document_management';

  /// Customer Service
  static const String serviceDashboard = 'service_dashboard';
  static const String tickets = 'support_tickets';
  static const String knowledgeBase = 'knowledge_base';
  static const String serviceContracts = 'service_contracts';
  static const String fieldService = 'field_service';
  static const String warranty = 'warranty_management';
  static const String feedback = 'customer_feedback';

  /// Business Intelligence
  static const String biDashboard = 'bi_dashboard';
  static const String dataVisualization = 'data_visualization';
  static const String kpis = 'key_performance_indicators';
  static const String businessReports = 'business_reports';
  static const String dataAnalytics = 'data_analytics';
  static const String predictiveAnalytics = 'predictive_analytics';

  /// Administration
  static const String userManagement = 'user_management';
  static const String roleManagement = 'role_management';
  static const String permissions = 'permissions';
  static const String auditLogs = 'audit_logs';
  static const String systemConfig = 'system_configuration';
  static const String backupRestore = 'backup_restore';
  static const String integrations = 'integrations';
  static const String apiManagement = 'api_management';

  /// Get all modules organized by category
  static Map<String, List<ERPModule>> getAllModules() {
    return {
      'Core': [
        ERPModule(dashboard, 'Dashboard', Icons.dashboard, 'Main dashboard with overview'),
        ERPModule(analytics, 'Analytics', Icons.analytics, 'Business analytics and insights'),
        ERPModule(reports, 'Reports', Icons.assessment, 'Generate and view reports'),
        ERPModule(settings, 'Settings', Icons.settings, 'System settings and configuration'),
      ],
      'Human Resources': [
        ERPModule(hrDashboard, 'HR Dashboard', Icons.people, 'Human resources overview'),
        ERPModule(employees, 'Employees', Icons.person, 'Employee management'),
        ERPModule(payroll, 'Payroll', Icons.payment, 'Payroll processing'),
        ERPModule(attendance, 'Attendance', Icons.access_time, 'Time and attendance tracking'),
        ERPModule(recruitment, 'Recruitment', Icons.person_add, 'Hiring and recruitment'),
        ERPModule(performance, 'Performance', Icons.trending_up, 'Performance management'),
        ERPModule(training, 'Training', Icons.school, 'Training and development'),
        ERPModule(benefits, 'Benefits', Icons.card_giftcard, 'Employee benefits'),
        ERPModule(leaves, 'Leave Management', Icons.event_busy, 'Leave requests and approvals'),
        ERPModule(expenses, 'Expenses', Icons.receipt, 'Expense management'),
      ],
      'Finance & Accounting': [
        ERPModule(financeDashboard, 'Finance Dashboard', Icons.account_balance, 'Financial overview'),
        ERPModule(accounts, 'Chart of Accounts', Icons.account_tree, 'Account management'),
        ERPModule(invoicing, 'Invoicing', Icons.description, 'Invoice generation'),
        ERPModule(billing, 'Billing', Icons.receipt_long, 'Billing management'),
        ERPModule(payments, 'Payments', Icons.payment, 'Payment processing'),
        ERPModule(budgeting, 'Budgeting', Icons.pie_chart, 'Budget planning'),
        ERPModule(taxation, 'Tax Management', Icons.calculate, 'Tax calculations'),
        ERPModule(assets, 'Fixed Assets', Icons.business, 'Asset management'),
        ERPModule(banking, 'Banking', Icons.account_balance_wallet, 'Bank reconciliation'),
        ERPModule(reconciliation, 'Reconciliation', Icons.balance, 'Account reconciliation'),
      ],
      'Sales & CRM': [
        ERPModule(salesDashboard, 'Sales Dashboard', Icons.trending_up, 'Sales performance overview'),
        ERPModule(leads, 'Leads', Icons.person_search, 'Lead management'),
        ERPModule(opportunities, 'Opportunities', Icons.star, 'Sales opportunities'),
        ERPModule(customers, 'Customers', Icons.people, 'Customer management'),
        ERPModule(contacts, 'Contacts', Icons.contact_phone, 'Contact management'),
        ERPModule(quotes, 'Quotes', Icons.request_quote, 'Quote generation'),
        ERPModule(orders, 'Sales Orders', Icons.shopping_cart, 'Order management'),
        ERPModule(campaigns, 'Campaigns', Icons.campaign, 'Marketing campaigns'),
        ERPModule(pipeline, 'Sales Pipeline', Icons.timeline, 'Sales pipeline tracking'),
        ERPModule(forecasting, 'Sales Forecasting', Icons.insights, 'Sales predictions'),
      ],
      'Inventory & Supply Chain': [
        ERPModule(inventoryDashboard, 'Inventory Dashboard', Icons.inventory, 'Inventory overview'),
        ERPModule(products, 'Products', Icons.category, 'Product catalog'),
        ERPModule(inventory, 'Stock Management', Icons.storage, 'Inventory tracking'),
        ERPModule(warehouses, 'Warehouses', Icons.warehouse, 'Warehouse management'),
        ERPModule(suppliers, 'Suppliers', Icons.local_shipping, 'Supplier management'),
        ERPModule(purchasing, 'Purchase Orders', Icons.shopping_bag, 'Purchase management'),
        ERPModule(stockMovements, 'Stock Movements', Icons.swap_horiz, 'Stock transfers'),
        ERPModule(qualityControl, 'Quality Control', Icons.verified, 'Quality assurance'),
        ERPModule(logistics, 'Logistics', Icons.local_shipping, 'Logistics management'),
        ERPModule(shipping, 'Shipping', Icons.delivery_dining, 'Shipping management'),
      ],
      'Manufacturing': [
        ERPModule(manufacturingDashboard, 'Manufacturing Dashboard', Icons.precision_manufacturing, 'Production overview'),
        ERPModule(production, 'Production', Icons.build, 'Production management'),
        ERPModule(workOrders, 'Work Orders', Icons.work, 'Work order management'),
        ERPModule(bom, 'Bill of Materials', Icons.list_alt, 'BOM management'),
        ERPModule(routing, 'Routing', Icons.route, 'Production routing'),
        ERPModule(capacity, 'Capacity Planning', Icons.schedule, 'Capacity management'),
        ERPModule(maintenance, 'Maintenance', Icons.build_circle, 'Equipment maintenance'),
        ERPModule(shopFloor, 'Shop Floor', Icons.factory, 'Shop floor control'),
        ERPModule(scheduling, 'Production Scheduling', Icons.event, 'Production planning'),
      ],
      'Project Management': [
        ERPModule(projectDashboard, 'Project Dashboard', Icons.dashboard, 'Project overview'),
        ERPModule(projects, 'Projects', Icons.folder, 'Project management'),
        ERPModule(tasks, 'Tasks', Icons.task, 'Task management'),
        ERPModule(milestones, 'Milestones', Icons.flag, 'Project milestones'),
        ERPModule(timeTracking, 'Time Tracking', Icons.timer, 'Time tracking'),
        ERPModule(resources, 'Resources', Icons.groups, 'Resource allocation'),
        ERPModule(gantt, 'Gantt Charts', Icons.timeline, 'Project timelines'),
        ERPModule(collaboration, 'Collaboration', Icons.group_work, 'Team collaboration'),
        ERPModule(documents, 'Documents', Icons.folder_shared, 'Document management'),
      ],
      'Customer Service': [
        ERPModule(serviceDashboard, 'Service Dashboard', Icons.support_agent, 'Service overview'),
        ERPModule(tickets, 'Support Tickets', Icons.confirmation_number, 'Ticket management'),
        ERPModule(knowledgeBase, 'Knowledge Base', Icons.library_books, 'Knowledge management'),
        ERPModule(serviceContracts, 'Service Contracts', Icons.description, 'Contract management'),
        ERPModule(fieldService, 'Field Service', Icons.engineering, 'Field service management'),
        ERPModule(warranty, 'Warranty', Icons.security, 'Warranty management'),
        ERPModule(feedback, 'Customer Feedback', Icons.feedback, 'Feedback management'),
      ],
      'Business Intelligence': [
        ERPModule(biDashboard, 'BI Dashboard', Icons.insights, 'Business intelligence overview'),
        ERPModule(dataVisualization, 'Data Visualization', Icons.bar_chart, 'Charts and graphs'),
        ERPModule(kpis, 'KPIs', Icons.speed, 'Key performance indicators'),
        ERPModule(businessReports, 'Business Reports', Icons.assessment, 'Business reporting'),
        ERPModule(dataAnalytics, 'Data Analytics', Icons.analytics, 'Data analysis'),
        ERPModule(predictiveAnalytics, 'Predictive Analytics', Icons.psychology, 'Predictive modeling'),
      ],
      'Administration': [
        ERPModule(userManagement, 'User Management', Icons.manage_accounts, 'User administration'),
        ERPModule(roleManagement, 'Role Management', Icons.admin_panel_settings, 'Role management'),
        ERPModule(permissions, 'Permissions', Icons.security, 'Permission management'),
        ERPModule(auditLogs, 'Audit Logs', Icons.history, 'System audit logs'),
        ERPModule(systemConfig, 'System Config', Icons.settings_applications, 'System configuration'),
        ERPModule(backupRestore, 'Backup & Restore', Icons.backup, 'Data backup and restore'),
        ERPModule(integrations, 'Integrations', Icons.integration_instructions, 'Third-party integrations'),
        ERPModule(apiManagement, 'API Management', Icons.api, 'API management'),
      ],
    };
  }

  /// Get module by ID
  static ERPModule? getModuleById(String id) {
    final allModules = getAllModules();
    for (final category in allModules.values) {
      for (final module in category) {
        if (module.id == id) return module;
      }
    }
    return null;
  }

  /// Get modules by category
  static List<ERPModule> getModulesByCategory(String category) {
    return getAllModules()[category] ?? [];
  }

  /// Get all module categories
  static List<String> getCategories() {
    return getAllModules().keys.toList();
  }
}

/// ERP Module Model
class ERPModule {
  final String id;
  final String name;
  final IconData icon;
  final String description;
  final bool isEnabled;
  final List<String> permissions;

  const ERPModule(
    this.id,
    this.name,
    this.icon,
    this.description, {
    this.isEnabled = true,
    this.permissions = const [],
  });

  ERPModule copyWith({
    String? id,
    String? name,
    IconData? icon,
    String? description,
    bool? isEnabled,
    List<String>? permissions,
  }) {
    return ERPModule(
      id ?? this.id,
      name ?? this.name,
      icon ?? this.icon,
      description ?? this.description,
      isEnabled: isEnabled ?? this.isEnabled,
      permissions: permissions ?? this.permissions,
    );
  }
}
