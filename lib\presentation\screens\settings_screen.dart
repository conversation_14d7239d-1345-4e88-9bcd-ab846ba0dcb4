import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../providers/auth_providers.dart';
import '../providers/theme_providers.dart';
import '../../data/services/database_stats_service.dart';
import 'help_screen.dart';

/// Settings screen for app preferences and configuration
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool _autoLoginEnabled = false;
  bool _notificationsEnabled = true;
  bool _requirePasswordAfterCrash = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final autoLogin = await ref
          .read(authServiceProvider)
          .isAutoLoginEnabled();
      setState(() {
        _autoLoginEnabled = autoLogin;
        // Other settings are loaded from their respective providers
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _toggleAutoLogin(bool value) async {
    try {
      await ref.read(authProvider.notifier).setAutoLogin(value);
      setState(() {
        _autoLoginEnabled = value;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(value ? 'Auto-login enabled' : 'Auto-login disabled'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update auto-login: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final isAdmin = authState.isAdmin;

    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Account Settings
          _buildSectionHeader('Account Settings'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Auto-login'),
                  subtitle: const Text('Automatically log in when app starts'),
                  value: _autoLoginEnabled,
                  onChanged: _toggleAutoLogin,
                ),
                if (isAdmin) ...[
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('Require password after crash'),
                    subtitle: const Text(
                      'Require password re-entry after app crash',
                    ),
                    value: _requirePasswordAfterCrash,
                    onChanged: (value) {
                      setState(() {
                        _requirePasswordAfterCrash = value;
                      });
                      // TODO: Save to storage
                    },
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Appearance Settings
          _buildSectionHeader('Appearance'),
          Card(
            child: Column(
              children: [
                Consumer(
                  builder: (context, ref, child) {
                    final themeState = ref.watch(themeProvider);

                    return ListTile(
                      leading: Icon(themeState.themeMode.icon),
                      title: const Text('Theme'),
                      subtitle: Text(
                        'Current: ${themeState.themeMode.displayName}',
                      ),
                      trailing: DropdownButton<AppThemeMode>(
                        value: themeState.themeMode,
                        onChanged: themeState.isLoading
                            ? null
                            : (AppThemeMode? newMode) {
                                if (newMode != null) {
                                  ref
                                      .read(themeProvider.notifier)
                                      .setThemeMode(newMode);
                                }
                              },
                        items: AppThemeMode.values.map((AppThemeMode mode) {
                          return DropdownMenuItem<AppThemeMode>(
                            value: mode,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(mode.icon, size: 16),
                                const SizedBox(width: 8),
                                Text(mode.displayName),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Notification Settings
          _buildSectionHeader('Notifications'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Task Notifications'),
                  subtitle: const Text('Receive notifications for due tasks'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                    // TODO: Save to storage
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Data & Storage
          _buildSectionHeader('Data & Storage'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.storage),
                  title: const Text('Database Size'),
                  subtitle: const Text('Calculate storage usage'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showDatabaseInfo();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.backup),
                  title: const Text('Backup Data'),
                  subtitle: const Text('Export your tasks and settings'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showBackupDialog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.restore),
                  title: const Text('Restore Data'),
                  subtitle: const Text('Import tasks from backup'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showRestoreDialog();
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.largePadding),

          // About & Support
          _buildSectionHeader('About & Support'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('About'),
                  subtitle: Text('Version ${AppConstants.appVersion}'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showAboutDialog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.help),
                  title: const Text('Help & Support'),
                  subtitle: const Text('Get help and report issues'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showHelpDialog();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConstants.defaultPadding,
        bottom: AppConstants.smallPadding,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showDatabaseInfo() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Calculating database statistics...'),
          ],
        ),
      ),
    );

    try {
      final stats = await DatabaseStatsService.instance.getDatabaseStatistics();
      final backupStats = await DatabaseStatsService.instance
          .getBackupDirectorySize();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Database Information'),
            content: SizedBox(
              width: 400,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Database Size
                    Text(
                      'Database Size',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total Size: ${stats['database_size']['formatted_size']}',
                    ),
                    Text(
                      'Size in MB: ${stats['database_size']['total_mb'].toStringAsFixed(2)} MB',
                    ),

                    const SizedBox(height: 16),

                    // Record Counts
                    Text(
                      'Record Counts',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total Tasks: ${stats['record_counts']['total_tasks']}',
                    ),
                    Text(
                      'Total Users: ${stats['record_counts']['total_users']}',
                    ),
                    Text(
                      'Active Users: ${stats['record_counts']['active_users']}',
                    ),

                    const SizedBox(height: 16),

                    // Storage Breakdown
                    Text(
                      'Storage Breakdown',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tasks: ${stats['storage_breakdown']['tasks']['formatted_size']} (${stats['storage_breakdown']['tasks']['percentage']}%)',
                    ),
                    Text(
                      'Users: ${stats['storage_breakdown']['users']['formatted_size']} (${stats['storage_breakdown']['users']['percentage']}%)',
                    ),
                    Text(
                      'System: ${stats['storage_breakdown']['system']['formatted_size']} (${stats['storage_breakdown']['system']['percentage']}%)',
                    ),

                    const SizedBox(height: 16),

                    // Backup Information
                    Text(
                      'Backup Storage',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Backup Files: ${backupStats['file_count']}'),
                    Text('Backup Size: ${backupStats['formatted_size']}'),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showDatabaseInfo(); // Refresh
                },
                child: const Text('Refresh'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to calculate database statistics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showBackupDialog() {
    Navigator.of(context).pushNamed('/backup-restore');
  }

  void _showRestoreDialog() {
    Navigator.of(context).pushNamed('/backup-restore');
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      children: [const Text('A secure desktop task management application.')],
    );
  }

  void _showHelpDialog() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const HelpScreen()));
  }
}
