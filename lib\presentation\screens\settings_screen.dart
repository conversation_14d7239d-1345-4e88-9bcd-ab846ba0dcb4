import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../providers/auth_providers.dart';

/// Settings screen for app preferences and configuration
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool _autoLoginEnabled = false;
  bool _darkModeEnabled = false;
  bool _notificationsEnabled = true;
  bool _requirePasswordAfterCrash = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final autoLogin = await ref.read(authServiceProvider).isAutoLoginEnabled();
      setState(() {
        _autoLoginEnabled = autoLogin;
        // TODO: Load other settings from storage
        _darkModeEnabled = Theme.of(context).brightness == Brightness.dark;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _toggleAutoLogin(bool value) async {
    try {
      await ref.read(authProvider.notifier).setAutoLogin(value);
      setState(() {
        _autoLoginEnabled = value;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              value ? 'Auto-login enabled' : 'Auto-login disabled',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update auto-login: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final isAdmin = authState.isAdmin;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Account Settings
          _buildSectionHeader('Account Settings'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Auto-login'),
                  subtitle: const Text('Automatically log in when app starts'),
                  value: _autoLoginEnabled,
                  onChanged: _toggleAutoLogin,
                ),
                if (isAdmin) ...[
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('Require password after crash'),
                    subtitle: const Text('Require password re-entry after app crash'),
                    value: _requirePasswordAfterCrash,
                    onChanged: (value) {
                      setState(() {
                        _requirePasswordAfterCrash = value;
                      });
                      // TODO: Save to storage
                    },
                  ),
                ],
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Appearance Settings
          _buildSectionHeader('Appearance'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Dark Mode'),
                  subtitle: const Text('Use dark theme'),
                  value: _darkModeEnabled,
                  onChanged: (value) {
                    setState(() {
                      _darkModeEnabled = value;
                    });
                    // TODO: Implement theme switching
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Theme switching will be implemented in next phase'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Notification Settings
          _buildSectionHeader('Notifications'),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Task Notifications'),
                  subtitle: const Text('Receive notifications for due tasks'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                    // TODO: Save to storage
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Data & Storage
          _buildSectionHeader('Data & Storage'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.storage),
                  title: const Text('Database Size'),
                  subtitle: const Text('Calculate storage usage'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showDatabaseInfo();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.backup),
                  title: const Text('Backup Data'),
                  subtitle: const Text('Export your tasks and settings'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showBackupDialog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.restore),
                  title: const Text('Restore Data'),
                  subtitle: const Text('Import tasks from backup'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showRestoreDialog();
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // About & Support
          _buildSectionHeader('About & Support'),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.info),
                  title: const Text('About'),
                  subtitle: Text('Version ${AppConstants.appVersion}'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showAboutDialog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.help),
                  title: const Text('Help & Support'),
                  subtitle: const Text('Get help and report issues'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    _showHelpDialog();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(
        left: AppConstants.defaultPadding,
        bottom: AppConstants.smallPadding,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _showDatabaseInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Database Information'),
        content: const Text('Database size calculation will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Backup Data'),
        content: const Text('Backup functionality will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Restore Data'),
        content: const Text('Restore functionality will be implemented in the next phase.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      children: [
        const Text('A secure desktop task management application.'),
      ],
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Need help? Here are some resources:'),
            SizedBox(height: 16),
            Text('• Check the user manual'),
            Text('• Contact support team'),
            Text('• Report bugs or issues'),
            Text('• Request new features'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
