import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// Mock implementation of secure storage for development/testing
/// This is a temporary solution until flutter_secure_storage Windows SDK issues are resolved
class MockSecureStorage {
  static MockSecureStorage? _instance;
  static MockSecureStorage get instance => _instance ??= MockSecureStorage._();
  
  MockSecureStorage._();

  late File _storageFile;
  Map<String, String> _storage = {};
  bool _initialized = false;

  /// Initialize the mock secure storage
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      final directory = await getApplicationDocumentsDirectory();
      final storageDirectory = Directory(path.join(directory.path, 'TaskManager', 'secure'));
      
      if (!await storageDirectory.exists()) {
        await storageDirectory.create(recursive: true);
      }

      _storageFile = File(path.join(storageDirectory.path, 'secure_storage.json'));
      
      if (await _storageFile.exists()) {
        final content = await _storageFile.readAsString();
        if (content.isNotEmpty) {
          final Map<String, dynamic> data = jsonDecode(content);
          _storage = data.cast<String, String>();
        }
      }

      _initialized = true;
    } catch (e) {
      print('Failed to initialize mock secure storage: $e');
      _storage = {};
      _initialized = true;
    }
  }

  /// Write a key-value pair to secure storage
  Future<void> write({required String key, required String value}) async {
    await initialize();
    
    _storage[key] = value;
    await _saveToFile();
  }

  /// Read a value from secure storage
  Future<String?> read({required String key}) async {
    await initialize();
    
    return _storage[key];
  }

  /// Delete a key from secure storage
  Future<void> delete({required String key}) async {
    await initialize();
    
    _storage.remove(key);
    await _saveToFile();
  }

  /// Delete all keys from secure storage
  Future<void> deleteAll() async {
    await initialize();
    
    _storage.clear();
    await _saveToFile();
  }

  /// Check if a key exists in secure storage
  Future<bool> containsKey({required String key}) async {
    await initialize();
    
    return _storage.containsKey(key);
  }

  /// Get all keys from secure storage
  Future<Set<String>> readAll() async {
    await initialize();
    
    return _storage.keys.toSet();
  }

  /// Save the storage map to file
  Future<void> _saveToFile() async {
    try {
      final content = jsonEncode(_storage);
      await _storageFile.writeAsString(content);
    } catch (e) {
      print('Failed to save secure storage: $e');
    }
  }

  /// Clear all data (for testing)
  Future<void> clear() async {
    _storage.clear();
    if (_initialized && await _storageFile.exists()) {
      await _storageFile.delete();
    }
  }
}

/// Mock FlutterSecureStorage class to replace the real one
class FlutterSecureStorage {
  const FlutterSecureStorage({
    AndroidOptions? aOptions,
    IOSOptions? iOptions,
    WindowsOptions? wOptions,
    LinuxOptions? lOptions,
  });

  Future<void> write({required String key, required String value}) async {
    await MockSecureStorage.instance.write(key: key, value: value);
  }

  Future<String?> read({required String key}) async {
    return await MockSecureStorage.instance.read(key: key);
  }

  Future<void> delete({required String key}) async {
    await MockSecureStorage.instance.delete(key: key);
  }

  Future<void> deleteAll() async {
    await MockSecureStorage.instance.deleteAll();
  }

  Future<bool> containsKey({required String key}) async {
    return await MockSecureStorage.instance.containsKey(key: key);
  }

  Future<Map<String, String>> readAll() async {
    await MockSecureStorage.instance.initialize();
    return MockSecureStorage.instance._storage;
  }
}

// Mock options classes
class AndroidOptions {
  final bool encryptedSharedPreferences;
  const AndroidOptions({this.encryptedSharedPreferences = true});
}

class IOSOptions {
  final KeychainAccessibility accessibility;
  const IOSOptions({this.accessibility = KeychainAccessibility.first_unlock_this_device});
}

class WindowsOptions {
  const WindowsOptions();
}

class LinuxOptions {
  const LinuxOptions();
}

enum KeychainAccessibility {
  first_unlock_this_device,
  first_unlock,
  unlocked_this_device,
  unlocked,
  passcode_set_this_device_only,
  when_passcode_set_this_device_only,
}
