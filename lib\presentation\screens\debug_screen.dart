import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/models/user.dart';
import '../../data/repositories/user_repository.dart';
import '../../data/services/user_management_service.dart';
import '../../core/utils/crypto_utils.dart';

/// Debug screen to help diagnose user login issues
class DebugScreen extends ConsumerStatefulWidget {
  const DebugScreen({super.key});

  @override
  ConsumerState<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends ConsumerState<DebugScreen> {
  final UserRepository _userRepository = UserRepository.instance;
  final UserManagementService _userManagementService =
      UserManagementService.instance;

  List<User> _allUsers = [];
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    setState(() {
      _isLoading = true;
      _debugInfo = 'Loading debug information...\n';
    });

    try {
      // Get all users (including inactive)
      _allUsers = await _userRepository.getAllUsers(includeInactive: true);

      final buffer = StringBuffer();
      buffer.writeln('=== USER DEBUG INFORMATION ===\n');

      buffer.writeln('Total users in database: ${_allUsers.length}');

      if (_allUsers.isEmpty) {
        buffer.writeln('No users found in database!');
        buffer.writeln('This might be why regular users cannot login.');
      } else {
        buffer.writeln('\nUser Details:');
        for (int i = 0; i < _allUsers.length; i++) {
          final user = _allUsers[i];
          buffer.writeln('${i + 1}. Username: ${user.username}');
          buffer.writeln('   Role: ${user.role}');
          buffer.writeln('   Active: ${user.isActive}');
          buffer.writeln('   Created: ${user.createdAt}');
          buffer.writeln('   Last Login: ${user.lastLoginAt ?? 'Never'}');
          buffer.writeln(
            '   Password Hash: ${user.passwordHash.substring(0, 20)}...',
          );
          buffer.writeln('');
        }
      }

      // Check admin user specifically
      final adminUser = await _userRepository.getUserByUsername(
        AppConstants.adminUsername,
      );
      buffer.writeln(
        'Admin user lookup result: ${adminUser != null ? 'Found' : 'Not found'}',
      );

      // Test password verification for admin
      if (adminUser != null) {
        final salt = _extractSalt(adminUser.passwordHash);
        final isValidPassword = CryptoUtils.verifyPassword(
          AppConstants.adminPassword,
          adminUser.passwordHash,
          salt,
        );
        buffer.writeln(
          'Admin password verification: ${isValidPassword ? 'Valid' : 'Invalid'}',
        );
      }

      setState(() {
        _debugInfo = buffer.toString();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _debugInfo = 'Error loading debug info: $e';
        _isLoading = false;
      });
    }
  }

  String _extractSalt(String storedHash) {
    final parts = storedHash.split(':');
    return parts.length > 1 ? parts[1] : '';
  }

  Future<void> _createTestUser() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // First check if user already exists (check all users, not just active)
      final allUsers = await _userRepository.getAllUsers(includeInactive: true);
      final existingUser = allUsers
          .where((user) => user.username == 'testuser')
          .firstOrNull;
      if (existingUser != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test user already exists!'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        setState(() {
          _isLoading = false;
        });
        await _loadDebugInfo();
        return;
      }

      await _userManagementService.createUser(
        username: 'testuser',
        password: 'password123',
        role: AppConstants.userRole,
        isActive: true,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Test user created successfully! Username: testuser, Password: password123',
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 5),
          ),
        );
      }

      await _loadDebugInfo();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create test user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testUserLogin() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Test login for testuser - check both active and inactive users
      final allUsers = await _userRepository.getAllUsers(includeInactive: true);
      final user = allUsers
          .where((user) => user.username == 'testuser')
          .firstOrNull;

      String result;
      if (user == null) {
        result = 'Test user not found in database (checked all users)';
      } else {
        final salt = _extractSalt(user.passwordHash);
        final isValidPassword = CryptoUtils.verifyPassword(
          'password123',
          user.passwordHash,
          salt,
        );
        result =
            'Test user found.\n'
            'Active: ${user.isActive}\n'
            'Role: ${user.role}\n'
            'Password verification: ${isValidPassword ? 'Valid' : 'Invalid'}\n'
            'Can login: ${user.isActive && isValidPassword ? 'YES' : 'NO'}';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result),
            backgroundColor: user != null && user.isActive
                ? Colors.green
                : Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Login test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteTestUser() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final user = await _userRepository.getUserByUsername('testuser');
      if (user != null) {
        await _userRepository.permanentlyDeleteUser(user.id!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test user deleted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Test user not found'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      await _loadDebugInfo();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete test user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Debug - User Login Issues')),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Action buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _loadDebugInfo,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh Info'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _createTestUser,
                  icon: const Icon(Icons.person_add),
                  label: const Text('Create Test User'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _testUserLogin,
                  icon: const Icon(Icons.login),
                  label: const Text('Test Login'),
                ),
                ElevatedButton.icon(
                  onPressed: _isLoading ? null : _deleteTestUser,
                  icon: const Icon(Icons.delete),
                  label: const Text('Delete Test User'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Debug information
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : SingleChildScrollView(
                          child: SelectableText(
                            _debugInfo,
                            style: const TextStyle(fontFamily: 'monospace'),
                          ),
                        ),
                ),
              ),
            ),

            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instructions:',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. Click "Refresh Info" to see current users'),
                    const Text(
                      '2. Click "Create Test User" to create testuser/password123',
                    ),
                    const Text(
                      '3. Click "Test Login" to verify the user can be found',
                    ),
                    const Text(
                      '4. Try logging in with testuser/password123 on login screen',
                    ),
                    const Text('5. Click "Delete Test User" when done testing'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
