import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/models/user.dart';
import '../../data/services/auth_service.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';

/// Provider for AuthService instance
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService.instance;
});

/// State class for authentication management
class AuthState {
  final User? user;
  final bool isLoading;
  final String? error;
  final bool isInitialized;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isInitialized = false,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isInitialized,
  }) {
    return AuthState(
      user: user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  bool get isLoggedIn => user != null;
  bool get isAdmin => user?.role == 'admin';
}

/// Notifier for managing authentication state
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier(this._authService) : super(const AuthState());

  final AuthService _authService;

  /// Initialize authentication
  Future<void> initialize() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await _authService.initialize();
      
      // Check if user is already logged in
      final currentUser = _authService.currentUser;
      
      state = state.copyWith(
        user: currentUser,
        isLoading: false,
        isInitialized: true,
      );
      
      await Logger.instance.info('Auth provider initialized');
    } catch (e) {
      await Logger.instance.error('Failed to initialize auth provider', e);
      state = state.copyWith(
        isLoading: false,
        isInitialized: true,
        error: e is AppException ? e.message : 'Failed to initialize authentication',
      );
    }
  }

  /// Login with username and password
  Future<bool> login({
    required String username,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await Logger.instance.info('Auth provider: Attempting login for $username');
      
      final user = await _authService.login(
        username: username,
        password: password,
        rememberMe: rememberMe,
      );

      state = state.copyWith(
        user: user,
        isLoading: false,
      );
      
      await Logger.instance.info('Auth provider: Login successful for ${user.username}');
      return true;
    } catch (e) {
      await Logger.instance.error('Auth provider: Login failed', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Login failed',
      );
      return false;
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await Logger.instance.info('Auth provider: Logging out user');
      
      await _authService.logout();
      
      state = state.copyWith(
        user: null,
        isLoading: false,
      );
      
      await Logger.instance.info('Auth provider: Logout successful');
    } catch (e) {
      await Logger.instance.error('Auth provider: Logout failed', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Logout failed',
      );
    }
  }

  /// Change password for current user
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      await Logger.instance.info('Auth provider: Changing password');
      
      await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      
      state = state.copyWith(isLoading: false);
      
      await Logger.instance.info('Auth provider: Password changed successfully');
      return true;
    } catch (e) {
      await Logger.instance.error('Auth provider: Password change failed', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Failed to change password',
      );
      return false;
    }
  }

  /// Set auto-login preference
  Future<void> setAutoLogin(bool enabled) async {
    try {
      await _authService.setAutoLogin(enabled);
      await Logger.instance.info('Auth provider: Auto-login ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      await Logger.instance.error('Auth provider: Failed to set auto-login', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to update auto-login setting',
      );
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh current user data
  Future<void> refreshUser() async {
    try {
      if (state.user?.id == null) return;
      
      // This would typically reload user data from the database
      // For now, we'll just keep the current user
      await Logger.instance.info('Auth provider: User data refreshed');
    } catch (e) {
      await Logger.instance.error('Auth provider: Failed to refresh user', e);
    }
  }
}

/// Provider for authentication state management
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

/// Provider for checking if auto-login is enabled
final autoLoginProvider = FutureProvider<bool>((ref) async {
  final authService = ref.watch(authServiceProvider);
  return await authService.isAutoLoginEnabled();
});

/// Provider for current user
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

/// Provider for checking if user is logged in
final isLoggedInProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isLoggedIn;
});

/// Provider for checking if current user is admin
final isAdminProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isAdmin;
});

/// Provider for authentication loading state
final authLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isLoading;
});

/// Provider for authentication error
final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.error;
});
