import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

/// Cryptographic utilities for the application
class CryptoUtils {
  /// Generate a secure random salt
  static String generateSalt([int length = 32]) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Hash a password with salt using PBKDF2
  static String hashPassword(String password, String salt) {
    final saltBytes = base64Decode(salt);
    final passwordBytes = utf8.encode(password);

    // Combine password and salt
    final combined = Uint8List.fromList([...passwordBytes, ...saltBytes]);

    // Use SHA-256 for hashing (PBKDF2 would be better but crypto package has limitations)
    var digest = sha256.convert(combined);

    // Apply multiple rounds for better security
    for (int i = 0; i < 10000; i++) {
      digest = sha256.convert([...digest.bytes, ...saltBytes]);
    }

    return base64Encode(digest.bytes);
  }

  /// Verify a password against its hash
  static bool verifyPassword(String password, String hash, String salt) {
    final computedHash = hashPassword(password, salt);
    return computedHash == hash;
  }

  /// Generate a secure random encryption key
  static String generateEncryptionKey([int length = 32]) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Generate a secure random string for various purposes
  static String generateSecureRandomString(int length) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  /// Create a hash for data integrity verification
  static String createDataHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return base64Encode(digest.bytes);
  }

  /// Verify data integrity using hash
  static bool verifyDataHash(String data, String expectedHash) {
    final computedHash = createDataHash(data);
    return computedHash == expectedHash;
  }

  /// Generate a session token
  static String generateSessionToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final randomPart = generateSecureRandomString(16);
    final combined = '$timestamp-$randomPart';
    return base64Encode(utf8.encode(combined));
  }

  /// Validate session token format
  static bool isValidSessionToken(String token) {
    try {
      final decoded = utf8.decode(base64Decode(token));
      final parts = decoded.split('-');
      if (parts.length != 2) return false;

      final timestamp = int.tryParse(parts[0]);
      return timestamp != null && parts[1].length == 16;
    } catch (e) {
      return false;
    }
  }

  /// Extract timestamp from session token
  static DateTime? getSessionTokenTimestamp(String token) {
    try {
      final decoded = utf8.decode(base64Decode(token));
      final parts = decoded.split('-');
      if (parts.length != 2) return null;

      final timestamp = int.tryParse(parts[0]);
      if (timestamp == null) return null;

      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      return null;
    }
  }

  /// Check if session token is expired
  static bool isSessionTokenExpired(String token, Duration maxAge) {
    final timestamp = getSessionTokenTimestamp(token);
    if (timestamp == null) return true;

    final now = DateTime.now();
    return now.difference(timestamp) > maxAge;
  }

  /// Validate encryption key format
  static bool isValidEncryptionKey(String key) {
    if (key.isEmpty) return false;
    // Accept both base64 and hex formats
    return key.length >= 32;
  }
}
