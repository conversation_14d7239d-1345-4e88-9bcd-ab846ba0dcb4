import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../repositories/task_repository.dart';
import '../repositories/user_repository.dart';

/// Service for calculating task statistics and analytics
class TaskStatisticsService {
  static TaskStatisticsService? _instance;
  static TaskStatisticsService get instance => _instance ??= TaskStatisticsService._();
  
  TaskStatisticsService._();

  final TaskRepository _taskRepository = TaskRepository.instance;
  final UserRepository _userRepository = UserRepository.instance;

  /// Get comprehensive task statistics
  Future<Map<String, dynamic>> getTaskStatistics() async {
    try {
      await Logger.instance.info('Calculating task statistics...');

      final tasks = await _taskRepository.getAllTasks();
      final users = await _userRepository.getAllUsers(includeInactive: false);

      // Basic counts
      final totalTasks = tasks.length;
      final totalUsers = users.length;

      // Status breakdown
      final statusBreakdown = _calculateStatusBreakdown(tasks);
      
      // Priority breakdown
      final priorityBreakdown = _calculatePriorityBreakdown(tasks);
      
      // Category breakdown
      final categoryBreakdown = _calculateCategoryBreakdown(tasks);
      
      // User task distribution
      final userTaskDistribution = _calculateUserTaskDistribution(tasks, users);
      
      // Time-based analytics
      final timeAnalytics = _calculateTimeAnalytics(tasks);
      
      // Completion metrics
      final completionMetrics = _calculateCompletionMetrics(tasks);
      
      // Tag analytics
      final tagAnalytics = _calculateTagAnalytics(tasks);

      final statistics = {
        'overview': {
          'total_tasks': totalTasks,
          'total_users': totalUsers,
          'completion_rate': completionMetrics['completion_rate'],
          'overdue_tasks': timeAnalytics['overdue_count'],
        },
        'status_breakdown': statusBreakdown,
        'priority_breakdown': priorityBreakdown,
        'category_breakdown': categoryBreakdown,
        'user_distribution': userTaskDistribution,
        'time_analytics': timeAnalytics,
        'completion_metrics': completionMetrics,
        'tag_analytics': tagAnalytics,
        'last_calculated': DateTime.now().toIso8601String(),
      };

      await Logger.instance.info('Task statistics calculated successfully');
      return statistics;
    } catch (e) {
      await Logger.instance.error('Failed to calculate task statistics', e);
      throw AppDatabaseException('Failed to calculate task statistics: $e');
    }
  }

  /// Calculate status breakdown
  Map<String, dynamic> _calculateStatusBreakdown(List<dynamic> tasks) {
    final breakdown = <String, int>{
      AppConstants.taskStatusPending: 0,
      AppConstants.taskStatusInProgress: 0,
      AppConstants.taskStatusCompleted: 0,
    };

    for (final task in tasks) {
      final status = task.status as String;
      breakdown[status] = (breakdown[status] ?? 0) + 1;
    }

    final total = tasks.length;
    return {
      'counts': breakdown,
      'percentages': breakdown.map((key, value) => MapEntry(
        key,
        total > 0 ? ((value / total) * 100).round() : 0,
      )),
      'total': total,
    };
  }

  /// Calculate priority breakdown
  Map<String, dynamic> _calculatePriorityBreakdown(List<dynamic> tasks) {
    final breakdown = <String, int>{
      AppConstants.taskPriorityLow: 0,
      AppConstants.taskPriorityMedium: 0,
      AppConstants.taskPriorityHigh: 0,
      AppConstants.taskPriorityCritical: 0,
    };

    for (final task in tasks) {
      final priority = task.priority as String;
      breakdown[priority] = (breakdown[priority] ?? 0) + 1;
    }

    final total = tasks.length;
    return {
      'counts': breakdown,
      'percentages': breakdown.map((key, value) => MapEntry(
        key,
        total > 0 ? ((value / total) * 100).round() : 0,
      )),
      'total': total,
    };
  }

  /// Calculate category breakdown
  Map<String, dynamic> _calculateCategoryBreakdown(List<dynamic> tasks) {
    final breakdown = <String, int>{};

    for (final task in tasks) {
      final category = task.category as String;
      breakdown[category] = (breakdown[category] ?? 0) + 1;
    }

    final total = tasks.length;
    return {
      'counts': breakdown,
      'percentages': breakdown.map((key, value) => MapEntry(
        key,
        total > 0 ? ((value / total) * 100).round() : 0,
      )),
      'total': total,
    };
  }

  /// Calculate user task distribution
  Map<String, dynamic> _calculateUserTaskDistribution(List<dynamic> tasks, List<dynamic> users) {
    final distribution = <int, Map<String, dynamic>>{};
    
    // Initialize for all users
    for (final user in users) {
      distribution[user.id] = {
        'username': user.username,
        'total_tasks': 0,
        'completed_tasks': 0,
        'pending_tasks': 0,
        'in_progress_tasks': 0,
      };
    }

    // Count tasks per user
    for (final task in tasks) {
      final userId = task.userId as int;
      if (distribution.containsKey(userId)) {
        distribution[userId]!['total_tasks'] = (distribution[userId]!['total_tasks'] as int) + 1;
        
        switch (task.status as String) {
          case AppConstants.taskStatusCompleted:
            distribution[userId]!['completed_tasks'] = (distribution[userId]!['completed_tasks'] as int) + 1;
            break;
          case AppConstants.taskStatusInProgress:
            distribution[userId]!['in_progress_tasks'] = (distribution[userId]!['in_progress_tasks'] as int) + 1;
            break;
          case AppConstants.taskStatusPending:
            distribution[userId]!['pending_tasks'] = (distribution[userId]!['pending_tasks'] as int) + 1;
            break;
        }
      }
    }

    return {
      'by_user': distribution,
      'most_active_user': _findMostActiveUser(distribution),
      'least_active_user': _findLeastActiveUser(distribution),
    };
  }

  /// Calculate time-based analytics
  Map<String, dynamic> _calculateTimeAnalytics(List<dynamic> tasks) {
    final now = DateTime.now();
    int overdueCount = 0;
    int dueTodayCount = 0;
    int dueThisWeekCount = 0;
    int noDueDateCount = 0;

    for (final task in tasks) {
      if (task.dueDate == null) {
        noDueDateCount++;
        continue;
      }

      final dueDate = task.dueDate as DateTime;
      final daysDifference = dueDate.difference(now).inDays;

      if (daysDifference < 0 && task.status != AppConstants.taskStatusCompleted) {
        overdueCount++;
      } else if (daysDifference == 0) {
        dueTodayCount++;
      } else if (daysDifference <= 7) {
        dueThisWeekCount++;
      }
    }

    return {
      'overdue_count': overdueCount,
      'due_today_count': dueTodayCount,
      'due_this_week_count': dueThisWeekCount,
      'no_due_date_count': noDueDateCount,
      'total_with_due_date': tasks.length - noDueDateCount,
    };
  }

  /// Calculate completion metrics
  Map<String, dynamic> _calculateCompletionMetrics(List<dynamic> tasks) {
    if (tasks.isEmpty) {
      return {
        'completion_rate': 0.0,
        'total_completed': 0,
        'total_incomplete': 0,
        'average_completion_time': 0.0,
      };
    }

    final completedTasks = tasks.where((task) => task.status == AppConstants.taskStatusCompleted).toList();
    final completionRate = (completedTasks.length / tasks.length) * 100;

    // Calculate average completion time (simplified - would need creation dates in real implementation)
    double averageCompletionTime = 0.0;
    if (completedTasks.isNotEmpty) {
      int totalDays = 0;
      int tasksWithDates = 0;
      
      for (final task in completedTasks) {
        if (task.completedAt != null) {
          final completedDate = task.completedAt as DateTime;
          final createdDate = task.createdAt as DateTime;
          totalDays += completedDate.difference(createdDate).inDays;
          tasksWithDates++;
        }
      }
      
      if (tasksWithDates > 0) {
        averageCompletionTime = totalDays / tasksWithDates;
      }
    }

    return {
      'completion_rate': completionRate.round(),
      'total_completed': completedTasks.length,
      'total_incomplete': tasks.length - completedTasks.length,
      'average_completion_time': averageCompletionTime.round(),
    };
  }

  /// Calculate tag analytics
  Map<String, dynamic> _calculateTagAnalytics(List<dynamic> tasks) {
    final tagCounts = <String, int>{};
    int totalTags = 0;

    for (final task in tasks) {
      final tags = task.tags as List<String>;
      totalTags += tags.length;
      
      for (final tag in tags) {
        if (tag.isNotEmpty) {
          tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
        }
      }
    }

    // Sort tags by usage
    final sortedTags = tagCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return {
      'total_unique_tags': tagCounts.length,
      'total_tag_usage': totalTags,
      'most_used_tags': sortedTags.take(10).map((entry) => {
        'tag': entry.key,
        'count': entry.value,
      }).toList(),
      'average_tags_per_task': tasks.isNotEmpty ? (totalTags / tasks.length).toStringAsFixed(1) : '0.0',
    };
  }

  /// Find most active user
  Map<String, dynamic>? _findMostActiveUser(Map<int, Map<String, dynamic>> distribution) {
    if (distribution.isEmpty) return null;

    var mostActive = distribution.entries.first;
    for (final entry in distribution.entries) {
      if ((entry.value['total_tasks'] as int) > (mostActive.value['total_tasks'] as int)) {
        mostActive = entry;
      }
    }

    return {
      'user_id': mostActive.key,
      'username': mostActive.value['username'],
      'task_count': mostActive.value['total_tasks'],
    };
  }

  /// Find least active user
  Map<String, dynamic>? _findLeastActiveUser(Map<int, Map<String, dynamic>> distribution) {
    if (distribution.isEmpty) return null;

    var leastActive = distribution.entries.first;
    for (final entry in distribution.entries) {
      if ((entry.value['total_tasks'] as int) < (leastActive.value['total_tasks'] as int)) {
        leastActive = entry;
      }
    }

    return {
      'user_id': leastActive.key,
      'username': leastActive.value['username'],
      'task_count': leastActive.value['total_tasks'],
    };
  }

  /// Get productivity insights
  Future<Map<String, dynamic>> getProductivityInsights() async {
    try {
      final statistics = await getTaskStatistics();
      
      final insights = <String, dynamic>{
        'recommendations': _generateRecommendations(statistics),
        'trends': _analyzeTrends(statistics),
        'alerts': _generateAlerts(statistics),
      };

      return insights;
    } catch (e) {
      await Logger.instance.error('Failed to generate productivity insights', e);
      throw AppDatabaseException('Failed to generate productivity insights: $e');
    }
  }

  /// Generate recommendations based on statistics
  List<String> _generateRecommendations(Map<String, dynamic> statistics) {
    final recommendations = <String>[];
    
    final completionRate = statistics['completion_metrics']['completion_rate'] as int;
    final overdueCount = statistics['time_analytics']['overdue_count'] as int;
    final priorityBreakdown = statistics['priority_breakdown']['counts'] as Map<String, int>;
    
    if (completionRate < 50) {
      recommendations.add('Consider breaking down large tasks into smaller, manageable pieces');
    }
    
    if (overdueCount > 0) {
      recommendations.add('Review overdue tasks and update due dates or priorities');
    }
    
    final criticalTasks = priorityBreakdown[AppConstants.taskPriorityCritical] ?? 0;
    if (criticalTasks > 5) {
      recommendations.add('You have many critical tasks - consider delegating or reprioritizing');
    }
    
    return recommendations;
  }

  /// Analyze trends (simplified version)
  Map<String, dynamic> _analyzeTrends(Map<String, dynamic> statistics) {
    return {
      'completion_trend': 'stable', // Would calculate from historical data
      'task_creation_trend': 'increasing', // Would calculate from creation dates
      'priority_trend': 'more_high_priority', // Would analyze priority changes over time
    };
  }

  /// Generate alerts based on statistics
  List<String> _generateAlerts(Map<String, dynamic> statistics) {
    final alerts = <String>[];
    
    final overdueCount = statistics['time_analytics']['overdue_count'] as int;
    final dueTodayCount = statistics['time_analytics']['due_today_count'] as int;
    
    if (overdueCount > 0) {
      alerts.add('$overdueCount task(s) are overdue');
    }
    
    if (dueTodayCount > 0) {
      alerts.add('$dueTodayCount task(s) are due today');
    }
    
    return alerts;
  }
}
