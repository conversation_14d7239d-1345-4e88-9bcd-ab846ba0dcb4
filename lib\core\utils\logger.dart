import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../constants/app_constants.dart';

/// Logger utility for application logging
class Logger {
  static Logger? _instance;
  static Logger get instance => _instance ??= Logger._();
  
  Logger._();

  late File _logFile;
  bool _initialized = false;

  /// Initialize the logger
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDirectory = Directory(path.join(directory.path, 'TaskManager', 'logs'));
      
      if (!await logDirectory.exists()) {
        await logDirectory.create(recursive: true);
      }

      final now = DateTime.now();
      final monthYear = DateFormat('yyyy_MM').format(now);
      final logFileName = 'log_$monthYear.txt';
      
      _logFile = File(path.join(logDirectory.path, logFileName));
      
      if (!await _logFile.exists()) {
        await _logFile.create();
        await _writeToFile('Logger initialized for $monthYear');
      }

      _initialized = true;
    } catch (e) {
      print('Failed to initialize logger: $e');
    }
  }

  /// Write a message to the log file
  Future<void> _writeToFile(String message) async {
    if (!_initialized) await initialize();
    
    try {
      final timestamp = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
      final logEntry = '[$timestamp] $message\n';
      
      await _logFile.writeAsString(logEntry, mode: FileMode.append);
      
      // Check file size and rotate if necessary
      await _rotateLogIfNeeded();
    } catch (e) {
      print('Failed to write to log file: $e');
    }
  }

  /// Rotate log file if it exceeds maximum size
  Future<void> _rotateLogIfNeeded() async {
    try {
      final fileSize = await _logFile.length();
      if (fileSize > AppConstants.maxLogFileSize) {
        final directory = _logFile.parent;
        final timestamp = DateFormat('yyyy_MM_dd_HH_mm_ss').format(DateTime.now());
        final archiveName = 'log_archive_$timestamp.txt';
        final archiveFile = File(path.join(directory.path, archiveName));
        
        await _logFile.copy(archiveFile.path);
        await _logFile.writeAsString('');
        
        await _writeToFile('Log file rotated. Archive: $archiveName');
      }
    } catch (e) {
      print('Failed to rotate log file: $e');
    }
  }

  /// Log an info message
  Future<void> info(String message) async {
    await _writeToFile('INFO: $message');
  }

  /// Log a warning message
  Future<void> warning(String message) async {
    await _writeToFile('WARNING: $message');
  }

  /// Log an error message
  Future<void> error(String message, [dynamic error, StackTrace? stackTrace]) async {
    var logMessage = 'ERROR: $message';
    if (error != null) {
      logMessage += ' | Error: $error';
    }
    if (stackTrace != null) {
      logMessage += ' | StackTrace: $stackTrace';
    }
    await _writeToFile(logMessage);
  }

  /// Log a debug message
  Future<void> debug(String message) async {
    await _writeToFile('DEBUG: $message');
  }

  /// Log user action
  Future<void> logUserAction(String username, String action, {String? details}) async {
    var message = 'USER_ACTION: $username - $action';
    if (details != null) {
      message += ' | Details: $details';
    }
    await _writeToFile(message);
  }

  /// Log authentication events
  Future<void> logAuth(String username, String event, {bool success = true}) async {
    final status = success ? 'SUCCESS' : 'FAILED';
    await _writeToFile('AUTH: $username - $event - $status');
  }

  /// Log database operations
  Future<void> logDatabase(String operation, {String? table, String? details}) async {
    var message = 'DATABASE: $operation';
    if (table != null) {
      message += ' | Table: $table';
    }
    if (details != null) {
      message += ' | Details: $details';
    }
    await _writeToFile(message);
  }

  /// Get current log file path
  String? get currentLogFilePath => _initialized ? _logFile.path : null;

  /// Get log directory path
  Future<String?> get logDirectoryPath async {
    if (!_initialized) await initialize();
    return _logFile.parent.path;
  }
}
