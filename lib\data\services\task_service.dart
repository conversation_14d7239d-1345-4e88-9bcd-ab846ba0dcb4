import '../../core/models/task.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../repositories/task_repository.dart';

/// Service class for task business logic
class TaskService {
  static TaskService? _instance;
  static TaskService get instance => _instance ??= TaskService._();

  TaskService._();

  final TaskRepository _taskRepository = TaskRepository.instance;

  /// Create a new task with validation
  Future<Task> createTask({
    required String title,
    String? description,
    DateTime? dueDate,
    String status = AppConstants.taskStatusPending,
    int userId = 1, // Default user ID for now
    bool isShared = false,
    int priority = 1,
    String? category,
    List<String>? tags,
  }) async {
    try {
      // Validate input
      if (title.trim().isEmpty) {
        throw ValidationException('Task title cannot be empty');
      }

      if (title.length > 200) {
        throw ValidationException('Task title cannot exceed 200 characters');
      }

      if (description != null && description.length > 1000) {
        throw ValidationException(
          'Task description cannot exceed 1000 characters',
        );
      }

      // Validate due date
      if (dueDate != null &&
          dueDate.isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
        throw ValidationException('Due date cannot be in the past');
      }

      // Validate status
      const validStatuses = [
        AppConstants.taskStatusPending,
        AppConstants.taskStatusInProgress,
        AppConstants.taskStatusCompleted,
      ];
      if (!validStatuses.contains(status)) {
        throw ValidationException('Invalid task status');
      }

      await Logger.instance.logUserAction(
        'system',
        'CREATE_TASK',
        details: 'Creating task: $title',
      );

      final task = Task(
        title: title.trim(),
        description: description?.trim(),
        status: status,
        createdAt: DateTime.now(),
        dueDate: dueDate,
        userId: userId,
        isShared: isShared,
      );

      final createdTask = await _taskRepository.createTask(task);

      await Logger.instance.info(
        'Task service: Task created successfully - ${createdTask.title}',
      );
      return createdTask;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to create task', e);
      rethrow;
    }
  }

  /// Get all tasks for a user
  Future<List<Task>> getUserTasks(int userId) async {
    try {
      await Logger.instance.logUserAction('user_$userId', 'GET_TASKS');

      final tasks = await _taskRepository.getAllTasks(userId: userId);

      await Logger.instance.info(
        'Task service: Retrieved ${tasks.length} tasks for user $userId',
      );
      return tasks;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to get user tasks', e);
      rethrow;
    }
  }

  /// Get all tasks (admin view)
  Future<List<Task>> getAllTasks() async {
    try {
      await Logger.instance.logUserAction('admin', 'GET_ALL_TASKS');

      final tasks = await _taskRepository.getAllTasks();

      await Logger.instance.info(
        'Task service: Retrieved ${tasks.length} total tasks',
      );
      return tasks;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to get all tasks', e);
      rethrow;
    }
  }

  /// Get task by ID
  Future<Task?> getTaskById(int id) async {
    try {
      if (id <= 0) {
        throw ValidationException('Invalid task ID');
      }

      return await _taskRepository.getTaskById(id);
    } catch (e) {
      await Logger.instance.error('Task service: Failed to get task by ID', e);
      rethrow;
    }
  }

  /// Update an existing task
  Future<Task> updateTask({
    required int id,
    String? title,
    String? description,
    String? status,
    DateTime? dueDate,
    bool? isShared,
    int? priority,
    String? category,
  }) async {
    try {
      // Get existing task
      final existingTask = await _taskRepository.getTaskById(id);
      if (existingTask == null) {
        throw AppDatabaseException('Task not found');
      }

      // Validate updates
      if (title != null) {
        if (title.trim().isEmpty) {
          throw ValidationException('Task title cannot be empty');
        }
        if (title.length > 200) {
          throw ValidationException('Task title cannot exceed 200 characters');
        }
      }

      if (description != null && description.length > 1000) {
        throw ValidationException(
          'Task description cannot exceed 1000 characters',
        );
      }

      if (dueDate != null &&
          dueDate.isBefore(DateTime.now().subtract(const Duration(days: 1)))) {
        throw ValidationException('Due date cannot be in the past');
      }

      if (status != null) {
        const validStatuses = [
          AppConstants.taskStatusPending,
          AppConstants.taskStatusInProgress,
          AppConstants.taskStatusCompleted,
        ];
        if (!validStatuses.contains(status)) {
          throw ValidationException('Invalid task status');
        }
      }

      await Logger.instance.logUserAction(
        'system',
        'UPDATE_TASK',
        details: 'Updating task ID: $id',
      );

      // Create updated task
      final updatedTask = existingTask.copyWith(
        title: title?.trim(),
        description: description?.trim(),
        status: status,
        dueDate: dueDate,
        isShared: isShared,
        completedAt: status == AppConstants.taskStatusCompleted
            ? DateTime.now()
            : existingTask.completedAt,
      );

      final result = await _taskRepository.updateTask(updatedTask);

      await Logger.instance.info(
        'Task service: Task updated successfully - ${result.title}',
      );
      return result;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to update task', e);
      rethrow;
    }
  }

  /// Delete a task
  Future<bool> deleteTask(int id) async {
    try {
      if (id <= 0) {
        throw ValidationException('Invalid task ID');
      }

      // Check if task exists
      final existingTask = await _taskRepository.getTaskById(id);
      if (existingTask == null) {
        throw AppDatabaseException('Task not found');
      }

      await Logger.instance.logUserAction(
        'system',
        'DELETE_TASK',
        details: 'Deleting task: ${existingTask.title}',
      );

      final result = await _taskRepository.deleteTask(id);

      if (result) {
        await Logger.instance.info(
          'Task service: Task deleted successfully - ${existingTask.title}',
        );
      }

      return result;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to delete task', e);
      rethrow;
    }
  }

  /// Mark task as completed
  Future<Task> completeTask(int id) async {
    try {
      await Logger.instance.logUserAction(
        'system',
        'COMPLETE_TASK',
        details: 'Completing task ID: $id',
      );

      final result = await _taskRepository.markTaskCompleted(id);

      await Logger.instance.info(
        'Task service: Task marked as completed - ${result.title}',
      );
      return result;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to complete task', e);
      rethrow;
    }
  }

  /// Get tasks by status
  Future<List<Task>> getTasksByStatus(String status, {int? userId}) async {
    try {
      const validStatuses = [
        AppConstants.taskStatusPending,
        AppConstants.taskStatusInProgress,
        AppConstants.taskStatusCompleted,
      ];

      if (!validStatuses.contains(status)) {
        throw ValidationException('Invalid task status');
      }

      final tasks = await _taskRepository.getAllTasks(
        userId: userId,
        status: status,
      );

      await Logger.instance.info(
        'Task service: Retrieved ${tasks.length} tasks with status: $status',
      );
      return tasks;
    } catch (e) {
      await Logger.instance.error(
        'Task service: Failed to get tasks by status',
        e,
      );
      rethrow;
    }
  }

  /// Get overdue tasks
  Future<List<Task>> getOverdueTasks({int? userId}) async {
    try {
      final allOverdue = await _taskRepository.getOverdueTasks();

      // Filter by user if specified
      final tasks = userId != null
          ? allOverdue.where((task) => task.userId == userId).toList()
          : allOverdue;

      await Logger.instance.info(
        'Task service: Retrieved ${tasks.length} overdue tasks',
      );
      return tasks;
    } catch (e) {
      await Logger.instance.error(
        'Task service: Failed to get overdue tasks',
        e,
      );
      rethrow;
    }
  }

  /// Search tasks
  Future<List<Task>> searchTasks(String query, {int? userId}) async {
    try {
      if (query.trim().isEmpty) {
        throw ValidationException('Search query cannot be empty');
      }

      if (query.length < 2) {
        throw ValidationException('Search query must be at least 2 characters');
      }

      final tasks = await _taskRepository.searchTasks(
        query.trim(),
        userId: userId,
      );

      await Logger.instance.info(
        'Task service: Found ${tasks.length} tasks for query: $query',
      );
      return tasks;
    } catch (e) {
      await Logger.instance.error('Task service: Failed to search tasks', e);
      rethrow;
    }
  }

  /// Get task statistics
  Future<Map<String, int>> getTaskStatistics({int? userId}) async {
    try {
      final stats = await _taskRepository.getTaskStatistics(userId: userId);

      await Logger.instance.info(
        'Task service: Retrieved task statistics: $stats',
      );
      return stats;
    } catch (e) {
      await Logger.instance.error(
        'Task service: Failed to get task statistics',
        e,
      );
      rethrow;
    }
  }
}
