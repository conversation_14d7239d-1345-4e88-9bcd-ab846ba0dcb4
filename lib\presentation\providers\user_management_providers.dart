import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/models/user.dart';
import '../../data/services/user_management_service.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';

/// Provider for UserManagementService instance
final userManagementServiceProvider = Provider<UserManagementService>((ref) {
  return UserManagementService.instance;
});

/// State class for user management
class UserManagementState {
  final List<User> users;
  final bool isLoading;
  final String? error;
  final Map<String, int>? statistics;

  const UserManagementState({
    this.users = const [],
    this.isLoading = false,
    this.error,
    this.statistics,
  });

  UserManagementState copyWith({
    List<User>? users,
    bool? isLoading,
    String? error,
    Map<String, int>? statistics,
  }) {
    return UserManagementState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      statistics: statistics ?? this.statistics,
    );
  }
}

/// Notifier for managing user management state
class UserManagementNotifier extends StateNotifier<UserManagementState> {
  UserManagementNotifier(this._userManagementService) : super(const UserManagementState());

  final UserManagementService _userManagementService;

  /// Load all users
  Future<void> loadUsers({bool includeInactive = false}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final users = await _userManagementService.getAllUsers(includeInactive: includeInactive);
      
      state = state.copyWith(
        users: users,
        isLoading: false,
      );
      
      await Logger.instance.info('User management provider: Loaded ${users.length} users');
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to load users', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Failed to load users',
      );
    }
  }

  /// Create a new user
  Future<bool> createUser({
    required String username,
    required String password,
    required String role,
    bool isActive = true,
  }) async {
    try {
      await Logger.instance.info('User management provider: Creating new user - $username');
      
      final newUser = await _userManagementService.createUser(
        username: username,
        password: password,
        role: role,
        isActive: isActive,
      );

      // Add the new user to the current list
      final updatedUsers = [newUser, ...state.users];
      state = state.copyWith(users: updatedUsers);
      
      await Logger.instance.info('User management provider: User created successfully');
      return true;
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to create user', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to create user',
      );
      return false;
    }
  }

  /// Update an existing user
  Future<bool> updateUser({
    required int userId,
    String? username,
    String? password,
    String? role,
    bool? isActive,
  }) async {
    try {
      await Logger.instance.info('User management provider: Updating user ID - $userId');
      
      final updatedUser = await _userManagementService.updateUser(
        userId: userId,
        username: username,
        password: password,
        role: role,
        isActive: isActive,
      );

      // Update the user in the current list
      final updatedUsers = state.users.map((user) {
        return user.id == userId ? updatedUser : user;
      }).toList();

      state = state.copyWith(users: updatedUsers);
      
      await Logger.instance.info('User management provider: User updated successfully');
      return true;
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to update user', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to update user',
      );
      return false;
    }
  }

  /// Delete a user
  Future<bool> deleteUser(int userId) async {
    try {
      await Logger.instance.info('User management provider: Deleting user ID - $userId');
      
      final success = await _userManagementService.deleteUser(userId);
      
      if (success) {
        // Remove the user from the current list
        final updatedUsers = state.users.where((user) => user.id != userId).toList();
        state = state.copyWith(users: updatedUsers);
        
        await Logger.instance.info('User management provider: User deleted successfully');
      }
      
      return success;
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to delete user', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to delete user',
      );
      return false;
    }
  }

  /// Activate a user
  Future<bool> activateUser(int userId) async {
    try {
      await Logger.instance.info('User management provider: Activating user ID - $userId');
      
      final success = await _userManagementService.activateUser(userId);
      
      if (success) {
        // Update the user in the current list
        final updatedUsers = state.users.map((user) {
          return user.id == userId ? user.copyWith(isActive: true) : user;
        }).toList();

        state = state.copyWith(users: updatedUsers);
        
        await Logger.instance.info('User management provider: User activated successfully');
      }
      
      return success;
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to activate user', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to activate user',
      );
      return false;
    }
  }

  /// Search users
  Future<void> searchUsers(String query) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final users = await _userManagementService.searchUsers(query);
      
      state = state.copyWith(
        users: users,
        isLoading: false,
      );
      
      await Logger.instance.info('User management provider: Search completed - ${users.length} results');
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to search users', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Failed to search users',
      );
    }
  }

  /// Load user statistics
  Future<void> loadStatistics() async {
    try {
      final stats = await _userManagementService.getUserStatistics();
      
      state = state.copyWith(statistics: stats);
      
      await Logger.instance.info('User management provider: Statistics loaded');
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to load statistics', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to load statistics',
      );
    }
  }

  /// Reset user password
  Future<String?> resetUserPassword(int userId) async {
    try {
      await Logger.instance.info('User management provider: Resetting password for user ID - $userId');
      
      final tempPassword = await _userManagementService.resetUserPassword(userId);
      
      await Logger.instance.info('User management provider: Password reset successfully');
      return tempPassword;
    } catch (e) {
      await Logger.instance.error('User management provider: Failed to reset password', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to reset password',
      );
      return null;
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh users
  Future<void> refresh({bool includeInactive = false}) async {
    await loadUsers(includeInactive: includeInactive);
  }
}

/// Provider for user management state
final userManagementProvider = StateNotifierProvider<UserManagementNotifier, UserManagementState>((ref) {
  final userManagementService = ref.watch(userManagementServiceProvider);
  return UserManagementNotifier(userManagementService);
});

/// Provider for user statistics
final userStatisticsProvider = FutureProvider<Map<String, int>>((ref) async {
  final userManagementService = ref.watch(userManagementServiceProvider);
  return await userManagementService.getUserStatistics();
});

/// Provider for checking username availability
final usernameAvailabilityProvider = FutureProvider.family<bool, String>((ref, username) async {
  final userManagementService = ref.watch(userManagementServiceProvider);
  return await userManagementService.isUsernameAvailable(username);
});
