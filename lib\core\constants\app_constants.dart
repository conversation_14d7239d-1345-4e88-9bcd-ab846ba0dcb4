/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'Task Manager';
  static const String appVersion = '1.0.0';

  // Database
  static const String databaseName = 'task_manager.db';
  static const int databaseVersion = 1;

  // Security
  static const String encryptionKeyAlias = 'task_manager_encryption_key';
  static const String userSessionKey = 'user_session';
  static const String autoLoginKey = 'auto_login_enabled';
  static const String requirePasswordAfterCrashKey =
      'require_password_after_crash';

  // Admin Credentials (hardcoded as per requirements)
  static const String adminUsername = 'admin';
  static const String adminPassword =
      'admin123'; // In production, this should be more secure

  // User Roles
  static const String adminRole = 'admin';
  static const String userRole = 'user';

  // Task Status
  static const String taskStatusPending = 'pending';
  static const String taskStatusInProgress = 'in_progress';
  static const String taskStatusCompleted = 'completed';

  // Task Priority
  static const String taskPriorityLow = 'low';
  static const String taskPriorityMedium = 'medium';
  static const String taskPriorityHigh = 'high';
  static const String taskPriorityCritical = 'critical';

  // Task Categories
  static const String taskCategoryGeneral = 'general';
  static const String taskCategoryWork = 'work';
  static const String taskCategoryPersonal = 'personal';
  static const String taskCategoryProject = 'project';
  static const String taskCategoryMeeting = 'meeting';
  static const String taskCategoryReminder = 'reminder';

  // Permissions
  static const String permissionCreateTask = 'create_task';
  static const String permissionEditTask = 'edit_task';
  static const String permissionDeleteTask = 'delete_task';
  static const String permissionViewSharedTasks = 'view_shared_tasks';

  // UI
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;

  // Logging
  static const String logFileName = 'log.txt';
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
}
