# 📋 TaskManager Pro - Complete User Guide & Application Flow

## 🚀 **Quick Start Guide**

### **Default Credentials**
- **Admin User:** `admin` / `admin123`
- **Test User:** Create through Admin Dashboard or use Debug Tools

### **First Time Setup**
1. **Launch Application** - Run `flutter run -d windows`
2. **Login as Admin** - Use default admin credentials
3. **Create Users** - Go to Admin Dashboard → User Management → Add User
4. **Start Managing Tasks** - Create, organize, and track your tasks

---

## 🔐 **Authentication & User Management**

### **Login Process**
1. **Enter Credentials** on the login screen
2. **Optional:** Check "Remember Me" for auto-login
3. **Access Level** determined by user role (Admin/User)

### **User Roles & Permissions**

#### **👑 Admin Users**
- ✅ **Full System Access** - All features available
- ✅ **User Management** - Create, edit, delete users
- ✅ **System Settings** - Configure security, backup, themes
- ✅ **Admin Dashboard** - Statistics, audit logs, system overview
- ✅ **Debug Tools** - Troubleshooting and diagnostics
- ✅ **Task Management** - Full CRUD operations on all tasks

#### **👤 Regular Users**
- ✅ **Task Management** - Create, edit, delete own tasks
- ✅ **View Shared Tasks** - Access tasks marked as shared
- ✅ **Personal Settings** - Change password, theme preferences
- ✅ **Backup Personal Data** - Export own tasks
- ❌ **User Management** - Cannot manage other users
- ❌ **System Settings** - Cannot access admin settings
- ❌ **Admin Dashboard** - No access to admin features

---

## 📱 **Application Flow & Navigation**

### **Main Navigation Structure**
```
📱 TaskManager Pro
├── 🏠 Task List (Home)
├── ➕ Add/Edit Task
├── 👤 User Profile
├── ⚙️ Settings
├── 📚 Help & Support
└── 👑 Admin Dashboard (Admin Only)
    ├── 👥 User Management
    ├── 📊 Task Statistics
    ├── 🔧 System Settings
    ├── 🔒 Security Overview
    ├── 📋 Audit Logs
    └── 🐛 Debug Tools
```

### **Detailed Application Flow**

#### **1. 🚀 Application Startup**
```
Splash Screen → Database Initialization → Auto-Login Check → Login/Dashboard
```

#### **2. 🔐 Authentication Flow**
```
Login Screen → Credential Validation → Role Check → Appropriate Dashboard
```

#### **3. 📋 Task Management Flow**
```
Task List → Filter/Search → View/Edit Task → Save Changes → Updated List
```

#### **4. 👥 User Management Flow (Admin)**
```
Admin Dashboard → User Management → Add/Edit User → Save → User List
```

---

## 🎯 **Detailed Feature Usage**

### **📋 Task Management**

#### **Creating Tasks**
1. **Click ➕ Button** on main screen
2. **Fill Required Fields:**
   - **Title** (Required)
   - **Description** (Optional)
   - **Status** (Pending/In Progress/Completed)
   - **Priority** (Low/Medium/High/Critical)
   - **Category** (Work/Personal/Project/Meeting/Reminder/General)
   - **Tags** (Comma-separated keywords)
   - **Due Date** (Optional with time)
   - **Shared** (Visible to all users)
3. **Save Task**

#### **Managing Tasks**
- **Edit:** Tap on any task to modify
- **Complete:** Change status to "Completed"
- **Filter:** Use filter chips (Status/Priority/Category)
- **Search:** Use search bar for text-based filtering
- **Sort:** Tasks automatically sorted by priority and due date

#### **Task Properties Explained**
- **🔴 Critical Priority:** Urgent, high-impact tasks
- **🟠 High Priority:** Important tasks requiring attention
- **🟡 Medium Priority:** Standard tasks
- **🟢 Low Priority:** Nice-to-have tasks
- **📁 Categories:** Organize tasks by type/context
- **🏷️ Tags:** Flexible labeling system for cross-cutting concerns

### **👥 User Management (Admin Only)**

#### **Creating Users**
1. **Admin Dashboard** → **User Management**
2. **Click ➕ Add User**
3. **Fill User Details:**
   - **Username** (3+ characters, unique)
   - **Password** (6+ characters)
   - **Role** (Admin/User)
   - **Active Status** (Enable/Disable account)
4. **Save User**

#### **Managing Users**
- **Edit User:** Click on user to modify details
- **Reset Password:** Use menu option to reset user password
- **Activate/Deactivate:** Toggle user account status
- **Delete User:** Permanently remove user (use with caution)
- **View Statistics:** See user activity and task counts

### **⚙️ System Configuration**

#### **Theme Settings**
1. **Settings** → **Appearance**
2. **Choose Theme:**
   - **Light Mode:** Bright, clean interface
   - **Dark Mode:** Dark background, easier on eyes
   - **System Mode:** Follows OS theme preference
3. **Changes Apply Immediately**

#### **Security Settings (Admin)**
1. **Admin Dashboard** → **System Settings**
2. **Configure Security:**
   - **Data Encryption:** SQLCipher AES-256 (Always enabled)
   - **Session Timeout:** Auto-logout duration
   - **Password Complexity:** Enforce strong passwords
   - **Max Login Attempts:** Prevent brute force attacks
   - **Audit Logging:** Track all user actions

#### **Backup & Restore**
1. **Settings** → **Backup & Restore** OR **Admin Dashboard** → **Backup & Restore**
2. **Create Backup:**
   - **Select Data:** Users, Tasks, or Both
   - **Choose Location:** Local file system
   - **Backup Created:** JSON format with timestamp
3. **Restore Data:**
   - **Select Backup File:** Choose from available backups
   - **Confirm Restore:** Data will be merged/replaced
   - **Validation:** System checks data integrity

---

## 🔧 **Troubleshooting Guide**

### **🚨 Common Issues & Solutions**

#### **❌ "User Cannot Login" Issue**
**Symptoms:** Regular users can't login, admin works fine

**Diagnosis Steps:**
1. **Access Debug Tools:** Admin Dashboard → 🐛 Debug icon
2. **Check User Database:** Click "Refresh Info"
3. **Verify User Exists:** Look for username in user list
4. **Check User Status:** Ensure `Active: true`
5. **Test Password:** Use "Test Login" function

**Solutions:**
```bash
# Solution 1: Create Test User
1. Debug Tools → "Create Test User"
2. Try login with: testuser / password123
3. If successful, issue is with specific user

# Solution 2: Check User Status
1. Admin Dashboard → User Management
2. Find user → Edit → Ensure "Active" is checked
3. Save changes

# Solution 3: Reset User Password
1. User Management → Select User → Menu → Reset Password
2. Set new password → Save
3. Try login with new password

# Solution 4: Recreate User
1. Delete problematic user (if safe)
2. Create new user with same username
3. Set appropriate role and password
```

#### **🔄 Database Issues**
**Symptoms:** App crashes, data not loading, corruption errors

**Solutions:**
```bash
# Check Database Statistics
1. Settings → Database Size
2. Review file sizes and record counts
3. Look for anomalies

# Clean Database
1. Admin Dashboard → System Settings → Database Management
2. "Cleanup Old Backups" to free space
3. "Reset to Defaults" if corruption suspected

# Backup Before Fixing
1. Create full backup before any major changes
2. Test restore functionality
3. Keep multiple backup versions
```

#### **🎨 Theme/UI Issues**
**Symptoms:** Interface looks broken, theme not applying

**Solutions:**
```bash
# Reset Theme
1. Settings → Appearance → System Mode
2. Restart application
3. Try different theme modes

# Clear App Data (Last Resort)
1. Close application
2. Delete app data folder
3. Restart app (will recreate defaults)
```

### **📊 Performance Optimization**

#### **Database Performance**
- **Regular Cleanup:** Delete old backups and completed tasks
- **Monitor Size:** Check database statistics regularly
- **Optimize Queries:** Large datasets may slow down filtering

#### **Memory Management**
- **Close Unused Screens:** Use back navigation properly
- **Restart Periodically:** Long-running sessions may accumulate memory
- **Monitor Task Count:** Very large task lists may impact performance

---

## 🔍 **Advanced Features**

### **📈 Analytics & Reporting**
- **Task Statistics:** Admin Dashboard → Task Overview
- **User Analytics:** User Management → Statistics
- **Completion Metrics:** Track productivity trends
- **Priority Distribution:** Understand workload balance

### **🔒 Security Features**
- **Database Encryption:** SQLCipher AES-256 encryption
- **Password Security:** PBKDF2 hashing with salt
- **Session Management:** Secure tokens with expiration
- **Audit Logging:** Complete action tracking
- **Role-Based Access:** Granular permission control

### **💾 Data Management**
- **Automated Backups:** Configure scheduled backups
- **Selective Restore:** Choose specific data to restore
- **Data Validation:** Integrity checks during operations
- **Export Options:** JSON format for data portability

---

## 🆘 **Getting Help**

### **Built-in Help System**
1. **Settings** → **Help & Support**
2. **Tabs Available:**
   - **Getting Started:** Basic setup and overview
   - **User Guide:** Detailed feature explanations
   - **FAQ:** Common questions and answers
   - **Support:** Contact information and system details

### **Debug Tools (Admin)**
1. **Admin Dashboard** → **🐛 Debug Icon**
2. **Available Tools:**
   - **User Database Inspection:** View all users and their status
   - **Test User Creation:** Create temporary test accounts
   - **Login Testing:** Verify authentication flow
   - **Password Verification:** Test password hashing/verification

### **System Information**
- **App Version:** Check current version
- **Database Version:** Verify database schema
- **Encryption Status:** Confirm security features
- **Log Files:** Review application logs for errors

---

## 📞 **Support & Contact**

### **Self-Help Resources**
1. **In-App Help:** Settings → Help & Support
2. **Debug Tools:** Admin Dashboard → Debug icon
3. **System Information:** Copy from Help → Support tab

### **Reporting Issues**
When reporting issues, include:
- **System Information** (from Help → Support)
- **Steps to Reproduce** the problem
- **Expected vs Actual** behavior
- **Screenshots** if applicable
- **Error Messages** (exact text)

---

**🎉 Enjoy using TaskManager Pro - Your Complete Task Management Solution!**
