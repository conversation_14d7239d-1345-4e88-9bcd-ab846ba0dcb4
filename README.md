# Task Manager

A secure desktop task management application built with Flutter, featuring SQLCipher encryption, user authentication, admin controls, and offline operation.

## Features

- **Secure Authentication**: User login with password hashing and session management
- **Encrypted Database**: SQLite database with SQLCipher encryption for data security
- **Role-Based Access**: Admin and user roles with granular permissions
- **Task Management**: Create, edit, delete, and organize tasks with due dates
- **Offline Operation**: Fully functional without internet connection
- **Backup & Restore**: Secure database backup and restore functionality
- **Dark/Light Theme**: User-configurable theme support
- **Audit Logging**: Comprehensive logging of user actions and system events

## Architecture

The application follows a clean architecture pattern with the following structure:

```
lib/
├── core/
│   ├── constants/          # Application constants
│   ├── exceptions/         # Custom exception classes
│   ├── models/            # Data models (User, Task, Permission)
│   └── utils/             # Utility classes (Logger, CryptoUtils)
├── data/
│   ├── database/          # Database schema and helpers
│   ├── repositories/      # Data access layer
│   └── services/          # Business logic services
└── presentation/
    ├── providers/         # Riverpod state management
    ├── screens/           # UI screens
    └── widgets/           # Reusable UI components
```

## Dependencies

- **flutter_riverpod**: State management
- **sqflite_sqlcipher**: Encrypted SQLite database
- **flutter_secure_storage**: Secure credential storage
- **crypto**: Cryptographic operations
- **path_provider**: File system access
- **file_picker**: File selection for backup/restore
- **intl**: Internationalization and date formatting
- **uuid**: Unique identifier generation

## Getting Started

### Prerequisites

- Flutter SDK (>=3.8.1)
- Dart SDK
- Desktop development setup for your platform

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd task_manager
   ```

2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Run the application:
   ```bash
   flutter run -d windows  # For Windows
   flutter run -d macos    # For macOS
   flutter run -d linux    # For Linux
   ```

### Default Admin Credentials

- **Username**: admin
- **Password**: admin123

## Security Features

- **Database Encryption**: All data is encrypted using SQLCipher with AES-256
- **Password Hashing**: User passwords are hashed using PBKDF2 with salt
- **Secure Storage**: Encryption keys stored in platform secure storage
- **Session Management**: Secure session tokens with expiration
- **Audit Logging**: All user actions are logged for security monitoring

## Development

### Running Tests

```bash
flutter test
```

### Building for Production

```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
