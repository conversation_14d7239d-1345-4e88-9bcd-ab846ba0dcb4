import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import '../../core/models/user.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../database/database_helper.dart';

/// Repository class for managing user data operations
class UserRepository {
  static UserRepository? _instance;
  static UserRepository get instance => _instance ??= UserRepository._();
  
  UserRepository._();

  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;

  /// Get database instance
  Future<Database> get _database async => await _databaseHelper.database;

  /// Create a new user
  Future<User> createUser(User user) async {
    try {
      await Logger.instance.logDatabase('CREATE', table: 'users', details: 'Creating user: ${user.username}');
      
      final db = await _database;
      
      // Check if username already exists
      final existingUser = await getUserByUsername(user.username);
      if (existingUser != null) {
        throw ValidationException('Username already exists');
      }

      final userMap = user.toMap();
      userMap.remove('id'); // Remove id for auto-increment
      
      final id = await db.insert('users', userMap);
      
      await Logger.instance.info('User created successfully with ID: $id');
      
      return user.copyWith(id: id);
    } catch (e) {
      await Logger.instance.error('Failed to create user', e);
      throw AppDatabaseException('Failed to create user: $e');
    }
  }

  /// Get user by ID
  Future<User?> getUserById(int id) async {
    try {
      await Logger.instance.logDatabase('READ', table: 'users', details: 'Fetching user with ID: $id');
      
      final db = await _database;
      final result = await db.query(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isNotEmpty) {
        final user = User.fromMap(result.first);
        await Logger.instance.info('User found: ${user.username}');
        return user;
      }
      
      await Logger.instance.warning('User not found with ID: $id');
      return null;
    } catch (e) {
      await Logger.instance.error('Failed to get user by ID', e);
      throw AppDatabaseException('Failed to get user: $e');
    }
  }

  /// Get user by username
  Future<User?> getUserByUsername(String username) async {
    try {
      await Logger.instance.logDatabase('READ', table: 'users', details: 'Fetching user: $username');
      
      final db = await _database;
      final result = await db.query(
        'users',
        where: 'username = ? AND is_active = 1',
        whereArgs: [username],
      );

      if (result.isNotEmpty) {
        final user = User.fromMap(result.first);
        await Logger.instance.info('User found: ${user.username}');
        return user;
      }
      
      await Logger.instance.info('User not found: $username');
      return null;
    } catch (e) {
      await Logger.instance.error('Failed to get user by username', e);
      throw AppDatabaseException('Failed to get user: $e');
    }
  }

  /// Get all users
  Future<List<User>> getAllUsers({bool includeInactive = false}) async {
    try {
      await Logger.instance.logDatabase('READ', table: 'users', details: 'Fetching all users');
      
      final db = await _database;
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause = 'WHERE is_active = 1';
      }

      final result = await db.rawQuery('''
        SELECT * FROM users 
        $whereClause
        ORDER BY created_at DESC
      ''', whereArgs);

      final users = result.map((map) => User.fromMap(map)).toList();
      
      await Logger.instance.info('Retrieved ${users.length} users');
      return users;
    } catch (e) {
      await Logger.instance.error('Failed to get users', e);
      throw AppDatabaseException('Failed to get users: $e');
    }
  }

  /// Update an existing user
  Future<User> updateUser(User user) async {
    try {
      if (user.id == null) {
        throw ValidationException('User ID is required for update');
      }

      await Logger.instance.logDatabase('UPDATE', table: 'users', details: 'Updating user: ${user.username}');
      
      final db = await _database;
      final userMap = user.toMap();
      
      final rowsAffected = await db.update(
        'users',
        userMap,
        where: 'id = ?',
        whereArgs: [user.id],
      );

      if (rowsAffected == 0) {
        throw AppDatabaseException('User not found or no changes made');
      }

      await Logger.instance.info('User updated successfully: ${user.username}');
      return user;
    } catch (e) {
      await Logger.instance.error('Failed to update user', e);
      throw AppDatabaseException('Failed to update user: $e');
    }
  }

  /// Delete a user (soft delete - mark as inactive)
  Future<bool> deleteUser(int id) async {
    try {
      await Logger.instance.logDatabase('DELETE', table: 'users', details: 'Deleting user with ID: $id');
      
      final user = await getUserById(id);
      if (user == null) {
        await Logger.instance.warning('User not found for deletion: $id');
        return false;
      }

      // Soft delete - mark as inactive
      final inactiveUser = user.copyWith(isActive: false);
      await updateUser(inactiveUser);

      await Logger.instance.info('User marked as inactive: ${user.username}');
      return true;
    } catch (e) {
      await Logger.instance.error('Failed to delete user', e);
      throw AppDatabaseException('Failed to delete user: $e');
    }
  }

  /// Permanently delete a user (hard delete)
  Future<bool> permanentlyDeleteUser(int id) async {
    try {
      await Logger.instance.logDatabase('HARD_DELETE', table: 'users', details: 'Permanently deleting user with ID: $id');
      
      final db = await _database;
      final rowsAffected = await db.delete(
        'users',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rowsAffected > 0) {
        await Logger.instance.info('User permanently deleted with ID: $id');
        return true;
      } else {
        await Logger.instance.warning('No user found to delete with ID: $id');
        return false;
      }
    } catch (e) {
      await Logger.instance.error('Failed to permanently delete user', e);
      throw AppDatabaseException('Failed to permanently delete user: $e');
    }
  }

  /// Activate a user
  Future<bool> activateUser(int id) async {
    try {
      final user = await getUserById(id);
      if (user == null) {
        return false;
      }

      if (user.isActive) {
        return true; // Already active
      }

      final activeUser = user.copyWith(isActive: true);
      await updateUser(activeUser);

      await Logger.instance.info('User activated: ${user.username}');
      return true;
    } catch (e) {
      await Logger.instance.error('Failed to activate user', e);
      throw AppDatabaseException('Failed to activate user: $e');
    }
  }

  /// Get users by role
  Future<List<User>> getUsersByRole(String role) async {
    try {
      await Logger.instance.logDatabase('READ', table: 'users', details: 'Fetching users with role: $role');
      
      final db = await _database;
      final result = await db.query(
        'users',
        where: 'role = ? AND is_active = 1',
        whereArgs: [role],
        orderBy: 'created_at DESC',
      );

      final users = result.map((map) => User.fromMap(map)).toList();
      
      await Logger.instance.info('Retrieved ${users.length} users with role: $role');
      return users;
    } catch (e) {
      await Logger.instance.error('Failed to get users by role', e);
      throw AppDatabaseException('Failed to get users by role: $e');
    }
  }

  /// Search users by username
  Future<List<User>> searchUsers(String query) async {
    try {
      await Logger.instance.logDatabase('READ', table: 'users', details: 'Searching users with query: $query');
      
      final db = await _database;
      final result = await db.query(
        'users',
        where: 'username LIKE ? AND is_active = 1',
        whereArgs: ['%$query%'],
        orderBy: 'username ASC',
      );

      final users = result.map((map) => User.fromMap(map)).toList();
      
      await Logger.instance.info('Found ${users.length} users matching query: $query');
      return users;
    } catch (e) {
      await Logger.instance.error('Failed to search users', e);
      throw AppDatabaseException('Failed to search users: $e');
    }
  }

  /// Get user statistics
  Future<Map<String, int>> getUserStatistics() async {
    try {
      await Logger.instance.logDatabase('READ', table: 'users', details: 'Fetching user statistics');
      
      final db = await _database;
      
      // Get total users
      final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM users');
      final totalUsers = totalResult.first['count'] as int;
      
      // Get active users
      final activeResult = await db.rawQuery('SELECT COUNT(*) as count FROM users WHERE is_active = 1');
      final activeUsers = activeResult.first['count'] as int;
      
      // Get users by role
      final roleResult = await db.rawQuery('''
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE is_active = 1 
        GROUP BY role
      ''');

      Map<String, int> stats = {
        'total': totalUsers,
        'active': activeUsers,
        'inactive': totalUsers - activeUsers,
        AppConstants.adminRole: 0,
        AppConstants.userRole: 0,
      };

      for (final row in roleResult) {
        stats[row['role'] as String] = row['count'] as int;
      }

      await Logger.instance.info('User statistics retrieved: $stats');
      return stats;
    } catch (e) {
      await Logger.instance.error('Failed to get user statistics', e);
      throw AppDatabaseException('Failed to get user statistics: $e');
    }
  }

  /// Check if username is available
  Future<bool> isUsernameAvailable(String username) async {
    try {
      final user = await getUserByUsername(username);
      return user == null;
    } catch (e) {
      await Logger.instance.error('Failed to check username availability', e);
      return false;
    }
  }
}
