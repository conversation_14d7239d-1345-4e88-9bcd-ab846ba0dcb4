import '../../core/models/user.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/utils/crypto_utils.dart';
import '../../core/constants/app_constants.dart';
import '../repositories/user_repository.dart';

/// Service class for user management operations (admin only)
class UserManagementService {
  static UserManagementService? _instance;
  static UserManagementService get instance => _instance ??= UserManagementService._();
  
  UserManagementService._();

  final UserRepository _userRepository = UserRepository.instance;

  /// Create a new user (admin only)
  Future<User> createUser({
    required String username,
    required String password,
    required String role,
    bool isActive = true,
  }) async {
    try {
      // Validate input
      if (username.trim().isEmpty) {
        throw ValidationException('Username cannot be empty');
      }

      if (username.length < 3) {
        throw ValidationException('Username must be at least 3 characters');
      }

      if (password.length < 6) {
        throw ValidationException('Password must be at least 6 characters');
      }

      if (role != AppConstants.adminRole && role != AppConstants.userRole) {
        throw ValidationException('Invalid role specified');
      }

      // Check if username already exists
      final existingUser = await _userRepository.getUserByUsername(username.trim());
      if (existingUser != null) {
        throw ValidationException('Username already exists');
      }

      await Logger.instance.logUserAction('admin', 'CREATE_USER', details: 'Creating user: $username');

      // Hash password
      final salt = CryptoUtils.generateSalt();
      final passwordHash = CryptoUtils.hashPassword(password, salt);
      final hashedPasswordWithSalt = '$passwordHash:$salt';

      final user = User(
        username: username.trim(),
        passwordHash: hashedPasswordWithSalt,
        role: role,
        createdAt: DateTime.now(),
        isActive: isActive,
      );

      final createdUser = await _userRepository.createUser(user);
      
      await Logger.instance.info('User management: User created successfully - ${createdUser.username}');
      return createdUser;
    } catch (e) {
      await Logger.instance.error('User management: Failed to create user', e);
      rethrow;
    }
  }

  /// Get all users (admin only)
  Future<List<User>> getAllUsers({bool includeInactive = false}) async {
    try {
      await Logger.instance.logUserAction('admin', 'GET_ALL_USERS');
      
      final users = await _userRepository.getAllUsers(includeInactive: includeInactive);
      
      await Logger.instance.info('User management: Retrieved ${users.length} users');
      return users;
    } catch (e) {
      await Logger.instance.error('User management: Failed to get all users', e);
      rethrow;
    }
  }

  /// Update user details (admin only)
  Future<User> updateUser({
    required int userId,
    String? username,
    String? password,
    String? role,
    bool? isActive,
  }) async {
    try {
      // Get existing user
      final existingUser = await _userRepository.getUserById(userId);
      if (existingUser == null) {
        throw AppDatabaseException('User not found');
      }

      // Validate updates
      if (username != null) {
        if (username.trim().isEmpty) {
          throw ValidationException('Username cannot be empty');
        }
        if (username.length < 3) {
          throw ValidationException('Username must be at least 3 characters');
        }
        
        // Check if new username already exists (excluding current user)
        final existingWithUsername = await _userRepository.getUserByUsername(username.trim());
        if (existingWithUsername != null && existingWithUsername.id != userId) {
          throw ValidationException('Username already exists');
        }
      }

      if (password != null && password.length < 6) {
        throw ValidationException('Password must be at least 6 characters');
      }

      if (role != null && role != AppConstants.adminRole && role != AppConstants.userRole) {
        throw ValidationException('Invalid role specified');
      }

      await Logger.instance.logUserAction('admin', 'UPDATE_USER', details: 'Updating user ID: $userId');

      // Prepare updated user
      String? newPasswordHash;
      if (password != null) {
        final salt = CryptoUtils.generateSalt();
        final passwordHash = CryptoUtils.hashPassword(password, salt);
        newPasswordHash = '$passwordHash:$salt';
      }

      final updatedUser = existingUser.copyWith(
        username: username?.trim(),
        passwordHash: newPasswordHash,
        role: role,
        isActive: isActive,
      );

      final result = await _userRepository.updateUser(updatedUser);
      
      await Logger.instance.info('User management: User updated successfully - ${result.username}');
      return result;
    } catch (e) {
      await Logger.instance.error('User management: Failed to update user', e);
      rethrow;
    }
  }

  /// Delete user (admin only)
  Future<bool> deleteUser(int userId) async {
    try {
      // Get user details for logging
      final user = await _userRepository.getUserById(userId);
      if (user == null) {
        throw AppDatabaseException('User not found');
      }

      // Prevent deleting admin users
      if (user.role == AppConstants.adminRole) {
        throw ValidationException('Cannot delete admin users');
      }

      await Logger.instance.logUserAction('admin', 'DELETE_USER', details: 'Deleting user: ${user.username}');

      final result = await _userRepository.deleteUser(userId);
      
      if (result) {
        await Logger.instance.info('User management: User deleted successfully - ${user.username}');
      }
      
      return result;
    } catch (e) {
      await Logger.instance.error('User management: Failed to delete user', e);
      rethrow;
    }
  }

  /// Activate user (admin only)
  Future<bool> activateUser(int userId) async {
    try {
      await Logger.instance.logUserAction('admin', 'ACTIVATE_USER', details: 'Activating user ID: $userId');
      
      final result = await _userRepository.activateUser(userId);
      
      if (result) {
        await Logger.instance.info('User management: User activated successfully');
      }
      
      return result;
    } catch (e) {
      await Logger.instance.error('User management: Failed to activate user', e);
      rethrow;
    }
  }

  /// Get users by role (admin only)
  Future<List<User>> getUsersByRole(String role) async {
    try {
      if (role != AppConstants.adminRole && role != AppConstants.userRole) {
        throw ValidationException('Invalid role specified');
      }

      final users = await _userRepository.getUsersByRole(role);
      
      await Logger.instance.info('User management: Retrieved ${users.length} users with role: $role');
      return users;
    } catch (e) {
      await Logger.instance.error('User management: Failed to get users by role', e);
      rethrow;
    }
  }

  /// Search users (admin only)
  Future<List<User>> searchUsers(String query) async {
    try {
      if (query.trim().isEmpty) {
        throw ValidationException('Search query cannot be empty');
      }

      if (query.length < 2) {
        throw ValidationException('Search query must be at least 2 characters');
      }

      final users = await _userRepository.searchUsers(query.trim());
      
      await Logger.instance.info('User management: Found ${users.length} users for query: $query');
      return users;
    } catch (e) {
      await Logger.instance.error('User management: Failed to search users', e);
      rethrow;
    }
  }

  /// Get user statistics (admin only)
  Future<Map<String, int>> getUserStatistics() async {
    try {
      final stats = await _userRepository.getUserStatistics();
      
      await Logger.instance.info('User management: Retrieved user statistics: $stats');
      return stats;
    } catch (e) {
      await Logger.instance.error('User management: Failed to get user statistics', e);
      rethrow;
    }
  }

  /// Reset user password (admin only)
  Future<String> resetUserPassword(int userId) async {
    try {
      final user = await _userRepository.getUserById(userId);
      if (user == null) {
        throw AppDatabaseException('User not found');
      }

      // Generate temporary password
      final tempPassword = CryptoUtils.generateSecureRandomString(8);
      
      await Logger.instance.logUserAction('admin', 'RESET_PASSWORD', details: 'Resetting password for user: ${user.username}');

      // Update user with new password
      await updateUser(userId: userId, password: tempPassword);
      
      await Logger.instance.info('User management: Password reset for user: ${user.username}');
      return tempPassword;
    } catch (e) {
      await Logger.instance.error('User management: Failed to reset password', e);
      rethrow;
    }
  }

  /// Check if username is available
  Future<bool> isUsernameAvailable(String username) async {
    try {
      return await _userRepository.isUsernameAvailable(username.trim());
    } catch (e) {
      await Logger.instance.error('User management: Failed to check username availability', e);
      return false;
    }
  }
}
