import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/design_system/app_design_system.dart';
import '../widgets/modern_components.dart';

/// ERP Dashboard Screen
/// Comprehensive dashboard with KPIs, charts, and business insights
class ERPDashboardScreen extends ConsumerStatefulWidget {
  const ERPDashboardScreen({super.key});

  @override
  ConsumerState<ERPDashboardScreen> createState() => _ERPDashboardScreenState();
}

class _ERPDashboardScreenState extends ConsumerState<ERPDashboardScreen> {
  String _selectedPeriod = 'This Month';
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;

    return Scaffold(
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      body: SingleChildScrollView(
        padding: EdgeInsets.all(spacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDashboardHeader(theme, spacing),
            SizedBox(height: spacing.lg),
            _buildKPICards(theme, spacing),
            SizedBox(height: spacing.lg),
            _buildChartsSection(theme, spacing),
            SizedBox(height: spacing.lg),
            _buildQuickActions(theme, spacing),
            SizedBox(height: spacing.lg),
            _buildRecentActivity(theme, spacing),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardHeader(ThemeData theme, dynamic spacing) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Dashboard Overview',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: spacing.xs),
              Text(
                'Welcome back! Here\'s what\'s happening in your business.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: spacing.lg),
        Container(
          padding: EdgeInsets.symmetric(horizontal: spacing.md, vertical: spacing.sm),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
            border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.2)),
          ),
          child: DropdownButton<String>(
            value: _selectedPeriod,
            underline: const SizedBox(),
            items: ['Today', 'This Week', 'This Month', 'This Quarter', 'This Year']
                .map((period) => DropdownMenuItem(
                      value: period,
                      child: Text(period),
                    ))
                .toList(),
            onChanged: (value) => setState(() => _selectedPeriod = value!),
          ),
        ),
      ],
    );
  }

  Widget _buildKPICards(ThemeData theme, dynamic spacing) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      crossAxisSpacing: spacing.md,
      mainAxisSpacing: spacing.md,
      childAspectRatio: 1.5,
      children: [
        _buildKPICard(
          'Total Revenue',
          '\$2,847,392',
          '+12.5%',
          Icons.trending_up,
          AppDesignSystem.statusColors.success,
          theme,
          spacing,
        ),
        _buildKPICard(
          'Active Projects',
          '247',
          '+8.2%',
          Icons.folder_open,
          AppDesignSystem.priorityColors.medium,
          theme,
          spacing,
        ),
        _buildKPICard(
          'Team Members',
          '1,429',
          '+3.1%',
          Icons.people,
          AppDesignSystem.priorityColors.high,
          theme,
          spacing,
        ),
        _buildKPICard(
          'Pending Tasks',
          '89',
          '-5.4%',
          Icons.pending_actions,
          AppDesignSystem.statusColors.warning,
          theme,
          spacing,
        ),
      ],
    );
  }

  Widget _buildKPICard(
    String title,
    String value,
    String change,
    IconData icon,
    Color color,
    ThemeData theme,
    dynamic spacing,
  ) {
    final isPositive = change.startsWith('+');
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(spacing.sm),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: spacing.xs,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: (isPositive ? AppDesignSystem.statusColors.success : AppDesignSystem.statusColors.error)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                ),
                child: Text(
                  change,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: isPositive ? AppDesignSystem.statusColors.success : AppDesignSystem.statusColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: spacing.md),
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: spacing.xs),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection(ThemeData theme, dynamic spacing) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: ModernCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      'Revenue Trend',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    ModernChip(
                      label: 'Monthly',
                      selected: true,
                      onTap: () {},
                    ),
                  ],
                ),
                SizedBox(height: spacing.lg),
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.show_chart,
                          size: 48,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(height: spacing.sm),
                        Text(
                          'Chart Component',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Text(
                          'Integration with charts library needed',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: spacing.md),
        Expanded(
          child: ModernCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Department Distribution',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: spacing.lg),
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.pie_chart,
                          size: 48,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        SizedBox(height: spacing.sm),
                        Text(
                          'Pie Chart',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions(ThemeData theme, dynamic spacing) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: spacing.lg),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 6,
            crossAxisSpacing: spacing.md,
            mainAxisSpacing: spacing.md,
            childAspectRatio: 1.2,
            children: [
              _buildQuickActionItem('New Project', Icons.add_box, theme, spacing),
              _buildQuickActionItem('Add Employee', Icons.person_add, theme, spacing),
              _buildQuickActionItem('Create Invoice', Icons.receipt, theme, spacing),
              _buildQuickActionItem('New Order', Icons.shopping_cart, theme, spacing),
              _buildQuickActionItem('Generate Report', Icons.assessment, theme, spacing),
              _buildQuickActionItem('View Analytics', Icons.analytics, theme, spacing),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionItem(String title, IconData icon, ThemeData theme, dynamic spacing) {
    return InkWell(
      onTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$title - Coming soon')),
        );
      },
      borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
      child: Container(
        padding: EdgeInsets.all(spacing.sm),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
          border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.2)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 24,
              color: theme.colorScheme.primary,
            ),
            SizedBox(height: spacing.xs),
            Text(
              title,
              style: theme.textTheme.labelSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(ThemeData theme, dynamic spacing) {
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Recent Activity',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {},
                child: const Text('View All'),
              ),
            ],
          ),
          SizedBox(height: spacing.lg),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => Divider(height: spacing.lg),
            itemBuilder: (context, index) => _buildActivityItem(theme, spacing, index),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(ThemeData theme, dynamic spacing, int index) {
    final activities = [
      ('New project "Mobile App Development" created', Icons.folder, '2 hours ago'),
      ('Invoice #INV-2024-001 generated for \$15,000', Icons.receipt, '4 hours ago'),
      ('Employee John Doe added to HR system', Icons.person_add, '6 hours ago'),
      ('Monthly sales report generated', Icons.assessment, '1 day ago'),
      ('System backup completed successfully', Icons.backup, '2 days ago'),
    ];

    final activity = activities[index];

    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(spacing.sm),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
          ),
          child: Icon(
            activity.$2,
            size: 16,
            color: theme.colorScheme.onPrimaryContainer,
          ),
        ),
        SizedBox(width: spacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                activity.$1,
                style: theme.textTheme.bodyMedium,
              ),
              SizedBox(height: spacing.xs),
              Text(
                activity.$3,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
