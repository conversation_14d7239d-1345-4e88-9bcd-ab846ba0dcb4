import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/design_system/app_design_system.dart';
import '../../core/constants/erp_modules.dart';
import '../widgets/erp_navigation.dart';
import '../providers/auth_providers.dart';
import 'erp_dashboard_screen.dart';
import 'task_list_screen.dart';
import 'user_management_screen.dart';
import 'settings_screen.dart';

/// Main ERP Application Screen
/// Central hub for all ERP modules with modern navigation
class ERPMainScreen extends ConsumerStatefulWidget {
  const ERPMainScreen({super.key});

  @override
  ConsumerState<ERPMainScreen> createState() => _ERPMainScreenState();
}

class _ERPMainScreenState extends ConsumerState<ERPMainScreen> {
  String _currentModule = ERPModules.dashboard;
  bool _isNavigationCollapsed = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;

    return Scaffold(
      body: Row(
        children: [
          // Navigation Sidebar
          if (!_isNavigationCollapsed)
            ERPNavigation(
              currentModule: _currentModule,
              onModuleSelected: _onModuleSelected,
            ),
          
          // Main Content Area
          Expanded(
            child: Column(
              children: [
                _buildTopBar(theme, spacing),
                Expanded(
                  child: _buildModuleContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar(ThemeData theme, dynamic spacing) {
    final currentModuleInfo = ERPModules.getModuleById(_currentModule);
    
    return Container(
      height: 64,
      padding: EdgeInsets.symmetric(horizontal: spacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Navigation Toggle
          IconButton(
            onPressed: () => setState(() {
              _isNavigationCollapsed = !_isNavigationCollapsed;
            }),
            icon: Icon(
              _isNavigationCollapsed ? Icons.menu : Icons.menu_open,
            ),
            tooltip: _isNavigationCollapsed ? 'Show Navigation' : 'Hide Navigation',
          ),
          
          SizedBox(width: spacing.md),
          
          // Module Info
          if (currentModuleInfo != null) ...[
            Icon(
              currentModuleInfo.icon,
              color: theme.colorScheme.primary,
              size: 24,
            ),
            SizedBox(width: spacing.sm),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  currentModuleInfo.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  currentModuleInfo.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
          
          const Spacer(),
          
          // Top Bar Actions
          _buildTopBarActions(theme, spacing),
        ],
      ),
    );
  }

  Widget _buildTopBarActions(ThemeData theme, dynamic spacing) {
    return Row(
      children: [
        // Global Search
        Container(
          width: 300,
          height: 40,
          child: TextField(
            decoration: InputDecoration(
              hintText: 'Global search...',
              prefixIcon: const Icon(Icons.search, size: 20),
              filled: true,
              fillColor: theme.colorScheme.surfaceContainerHighest,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.full),
                borderSide: BorderSide.none,
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: spacing.md,
                vertical: spacing.xs,
              ),
            ),
          ),
        ),
        
        SizedBox(width: spacing.md),
        
        // Notifications
        IconButton(
          onPressed: () => _showNotifications(),
          icon: Stack(
            children: [
              const Icon(Icons.notifications_outlined),
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.error,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
          tooltip: 'Notifications',
        ),
        
        // Quick Actions
        IconButton(
          onPressed: () => _showQuickActions(),
          icon: const Icon(Icons.apps),
          tooltip: 'Quick Actions',
        ),
        
        // User Menu
        PopupMenuButton<String>(
          onSelected: _onUserMenuSelected,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: ListTile(
                leading: Icon(Icons.person),
                title: Text('Profile'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'preferences',
              child: ListTile(
                leading: Icon(Icons.tune),
                title: Text('Preferences'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: ListTile(
                leading: Icon(Icons.logout, color: Colors.red),
                title: Text('Logout', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
          child: Container(
            padding: EdgeInsets.all(spacing.xs),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colorScheme.primaryContainer,
            ),
            child: Icon(
              Icons.person,
              color: theme.colorScheme.onPrimaryContainer,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModuleContent() {
    switch (_currentModule) {
      case ERPModules.dashboard:
        return const ERPDashboardScreen();
      case ERPModules.tasks:
        return const TaskListScreen();
      case ERPModules.userManagement:
        return const UserManagementScreen();
      case ERPModules.settings:
        return const SettingsScreen();
      default:
        return _buildPlaceholderModule();
    }
  }

  Widget _buildPlaceholderModule() {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    final currentModuleInfo = ERPModules.getModuleById(_currentModule);

    return Center(
      child: Container(
        padding: EdgeInsets.all(spacing.xl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(spacing.xl),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                currentModuleInfo?.icon ?? Icons.construction,
                size: 64,
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),
            SizedBox(height: spacing.lg),
            Text(
              currentModuleInfo?.name ?? 'Module',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: spacing.sm),
            Text(
              'This module is under development',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: spacing.lg),
            Container(
              padding: EdgeInsets.all(spacing.lg),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.medium),
              ),
              child: Column(
                children: [
                  Text(
                    'Coming Soon',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: spacing.sm),
                  Text(
                    currentModuleInfo?.description ?? 'Module description',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onModuleSelected(String moduleId) {
    setState(() {
      _currentModule = moduleId;
    });
  }

  void _showNotifications() {
    // TODO: Implement notifications panel
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notifications panel - Coming soon'),
      ),
    );
  }

  void _showQuickActions() {
    // TODO: Implement quick actions menu
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Quick actions menu - Coming soon'),
      ),
    );
  }

  void _onUserMenuSelected(String value) {
    switch (value) {
      case 'profile':
        // TODO: Navigate to profile
        break;
      case 'preferences':
        // TODO: Navigate to preferences
        break;
      case 'logout':
        ref.read(authProvider.notifier).logout();
        break;
    }
  }
}
