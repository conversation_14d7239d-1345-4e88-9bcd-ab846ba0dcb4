import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';

/// Database helper class for managing SQLite database operations
class DatabaseHelper {
  static DatabaseHelper? _instance;
  static Database? _database;

  DatabaseHelper._();

  static DatabaseHelper get instance => _instance ??= DatabaseHelper._();

  /// Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// Initialize the database
  Future<Database> _initDatabase() async {
    try {
      await Logger.instance.info('Initializing database...');

      final documentsDirectory = await getApplicationDocumentsDirectory();
      final dbDirectory = Directory(
        join(documentsDirectory.path, 'TaskManager'),
      );

      if (!await dbDirectory.exists()) {
        await dbDirectory.create(recursive: true);
      }

      final dbPath = join(dbDirectory.path, AppConstants.databaseName);

      await Logger.instance.info('Database path: $dbPath');

      final database = await openDatabase(
        dbPath,
        version: AppConstants.databaseVersion,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
        onOpen: (db) async {
          await Logger.instance.info('Database opened successfully');
        },
      );

      await Logger.instance.info('Database initialized successfully');
      return database;
    } catch (e) {
      await Logger.instance.error('Failed to initialize database', e);
      throw AppDatabaseException('Failed to initialize database: $e');
    }
  }

  /// Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    try {
      await Logger.instance.info('Creating database tables...');

      // Create tasks table
      await db.execute('''
        CREATE TABLE tasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT,
          status TEXT NOT NULL DEFAULT '${AppConstants.taskStatusPending}',
          created_at TEXT NOT NULL,
          due_date TEXT,
          completed_at TEXT,
          user_id INTEGER NOT NULL DEFAULT 1,
          is_shared INTEGER NOT NULL DEFAULT 0,
          priority INTEGER NOT NULL DEFAULT 1,
          category TEXT,
          tags TEXT
        )
      ''');

      // Create users table (basic structure for future use)
      await db.execute('''
        CREATE TABLE users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT UNIQUE NOT NULL,
          password_hash TEXT NOT NULL,
          salt TEXT NOT NULL,
          role TEXT NOT NULL DEFAULT '${AppConstants.userRole}',
          created_at TEXT NOT NULL,
          last_login_at TEXT,
          is_active INTEGER NOT NULL DEFAULT 1
        )
      ''');

      // Create default admin user
      await _createDefaultUser(db);

      // Create indexes for better performance
      await db.execute('CREATE INDEX idx_tasks_status ON tasks(status)');
      await db.execute('CREATE INDEX idx_tasks_user_id ON tasks(user_id)');
      await db.execute('CREATE INDEX idx_tasks_due_date ON tasks(due_date)');
      await db.execute(
        'CREATE INDEX idx_tasks_created_at ON tasks(created_at)',
      );

      await Logger.instance.info('Database tables created successfully');
    } catch (e) {
      await Logger.instance.error('Failed to create database tables', e);
      throw AppDatabaseException('Failed to create database tables: $e');
    }
  }

  /// Create default admin user
  Future<void> _createDefaultUser(Database db) async {
    try {
      // For now, we'll create a simple default user
      // In the next phase, we'll implement proper password hashing
      await db.insert('users', {
        'username': AppConstants.adminUsername,
        'password_hash': 'temp_hash', // Will be replaced with proper hashing
        'salt': 'temp_salt',
        'role': AppConstants.adminRole,
        'created_at': DateTime.now().toIso8601String(),
        'is_active': 1,
      });

      await Logger.instance.info('Default admin user created');
    } catch (e) {
      await Logger.instance.error('Failed to create default user', e);
    }
  }

  /// Upgrade database schema
  Future<void> _upgradeDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    try {
      await Logger.instance.info(
        'Upgrading database from version $oldVersion to $newVersion',
      );

      // Handle database migrations here
      if (oldVersion < 2) {
        // Example migration for version 2
        // await db.execute('ALTER TABLE tasks ADD COLUMN priority INTEGER DEFAULT 1');
      }

      await Logger.instance.info('Database upgraded successfully');
    } catch (e) {
      await Logger.instance.error('Failed to upgrade database', e);
      throw AppDatabaseException('Failed to upgrade database: $e');
    }
  }

  /// Close database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      await Logger.instance.info('Database connection closed');
    }
  }

  /// Delete database (for testing purposes)
  Future<void> deleteDatabase() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final dbPath = join(
        documentsDirectory.path,
        'TaskManager',
        AppConstants.databaseName,
      );

      if (await File(dbPath).exists()) {
        await File(dbPath).delete();
        await Logger.instance.info('Database deleted');
      }

      _database = null;
    } catch (e) {
      await Logger.instance.error('Failed to delete database', e);
      throw AppDatabaseException('Failed to delete database: $e');
    }
  }

  /// Get database file size
  Future<int> getDatabaseSize() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final dbPath = join(
        documentsDirectory.path,
        'TaskManager',
        AppConstants.databaseName,
      );

      if (await File(dbPath).exists()) {
        return await File(dbPath).length();
      }
      return 0;
    } catch (e) {
      await Logger.instance.error('Failed to get database size', e);
      return 0;
    }
  }

  /// Check if database exists
  Future<bool> databaseExists() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final dbPath = join(
        documentsDirectory.path,
        'TaskManager',
        AppConstants.databaseName,
      );
      return await File(dbPath).exists();
    } catch (e) {
      return false;
    }
  }
}
