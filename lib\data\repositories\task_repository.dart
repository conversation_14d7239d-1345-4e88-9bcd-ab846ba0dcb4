import 'package:sqflite/sqflite.dart';
import '../../core/models/task.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../database/database_helper.dart';

/// Repository class for managing task data operations
class TaskRepository {
  static TaskRepository? _instance;
  static TaskRepository get instance => _instance ??= TaskRepository._();

  TaskRepository._();

  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;

  /// Get database instance
  Future<Database> get _database async => await _databaseHelper.database;

  /// Create a new task
  Future<Task> createTask(Task task) async {
    try {
      await Logger.instance.logDatabase(
        'CREATE',
        table: 'tasks',
        details: 'Creating task: ${task.title}',
      );

      final db = await _database;
      final taskMap = task.toMap();
      taskMap.remove('id'); // Remove id for auto-increment

      final id = await db.insert('tasks', taskMap);

      await Logger.instance.info('Task created successfully with ID: $id');

      return task.copyWith(id: id);
    } catch (e) {
      await Logger.instance.error('Failed to create task', e);
      throw AppDatabaseException('Failed to create task: $e');
    }
  }

  /// Get all tasks
  Future<List<Task>> getAllTasks({int? userId, String? status}) async {
    try {
      await Logger.instance.logDatabase(
        'READ',
        table: 'tasks',
        details: 'Fetching all tasks',
      );

      final db = await _database;
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (userId != null || status != null) {
        List<String> conditions = [];

        if (userId != null) {
          conditions.add('user_id = ?');
          whereArgs.add(userId);
        }

        if (status != null) {
          conditions.add('status = ?');
          whereArgs.add(status);
        }

        whereClause = 'WHERE ${conditions.join(' AND ')}';
      }

      final result = await db.rawQuery('''
        SELECT * FROM tasks 
        $whereClause
        ORDER BY created_at DESC
      ''', whereArgs);

      final tasks = result.map((map) => Task.fromMap(map)).toList();

      await Logger.instance.info('Retrieved ${tasks.length} tasks');
      return tasks;
    } catch (e) {
      await Logger.instance.error('Failed to get tasks', e);
      throw AppDatabaseException('Failed to get tasks: $e');
    }
  }

  /// Get task by ID
  Future<Task?> getTaskById(int id) async {
    try {
      await Logger.instance.logDatabase(
        'READ',
        table: 'tasks',
        details: 'Fetching task with ID: $id',
      );

      final db = await _database;
      final result = await db.query('tasks', where: 'id = ?', whereArgs: [id]);

      if (result.isNotEmpty) {
        final task = Task.fromMap(result.first);
        await Logger.instance.info('Task found: ${task.title}');
        return task;
      }

      await Logger.instance.warning('Task not found with ID: $id');
      return null;
    } catch (e) {
      await Logger.instance.error('Failed to get task by ID', e);
      throw AppDatabaseException('Failed to get task: $e');
    }
  }

  /// Update an existing task
  Future<Task> updateTask(Task task) async {
    try {
      if (task.id == null) {
        throw ValidationException('Task ID is required for update');
      }

      await Logger.instance.logDatabase(
        'UPDATE',
        table: 'tasks',
        details: 'Updating task: ${task.title}',
      );

      final db = await _database;
      final taskMap = task.toMap();

      final rowsAffected = await db.update(
        'tasks',
        taskMap,
        where: 'id = ?',
        whereArgs: [task.id],
      );

      if (rowsAffected == 0) {
        throw AppDatabaseException('Task not found or no changes made');
      }

      await Logger.instance.info('Task updated successfully: ${task.title}');
      return task;
    } catch (e) {
      await Logger.instance.error('Failed to update task', e);
      throw AppDatabaseException('Failed to update task: $e');
    }
  }

  /// Delete a task
  Future<bool> deleteTask(int id) async {
    try {
      await Logger.instance.logDatabase(
        'DELETE',
        table: 'tasks',
        details: 'Deleting task with ID: $id',
      );

      final db = await _database;
      final rowsAffected = await db.delete(
        'tasks',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rowsAffected > 0) {
        await Logger.instance.info('Task deleted successfully with ID: $id');
        return true;
      } else {
        await Logger.instance.warning('No task found to delete with ID: $id');
        return false;
      }
    } catch (e) {
      await Logger.instance.error('Failed to delete task', e);
      throw AppDatabaseException('Failed to delete task: $e');
    }
  }

  /// Get tasks by status
  Future<List<Task>> getTasksByStatus(String status) async {
    return await getAllTasks(status: status);
  }

  /// Get overdue tasks
  Future<List<Task>> getOverdueTasks() async {
    try {
      await Logger.instance.logDatabase(
        'READ',
        table: 'tasks',
        details: 'Fetching overdue tasks',
      );

      final db = await _database;
      final now = DateTime.now().toIso8601String();

      final result = await db.rawQuery(
        '''
        SELECT * FROM tasks 
        WHERE due_date IS NOT NULL 
        AND due_date < ? 
        AND status != ?
        ORDER BY due_date ASC
      ''',
        [now, AppConstants.taskStatusCompleted],
      );

      final tasks = result.map((map) => Task.fromMap(map)).toList();

      await Logger.instance.info('Retrieved ${tasks.length} overdue tasks');
      return tasks;
    } catch (e) {
      await Logger.instance.error('Failed to get overdue tasks', e);
      throw AppDatabaseException('Failed to get overdue tasks: $e');
    }
  }

  /// Mark task as completed
  Future<Task> markTaskCompleted(int id) async {
    try {
      final task = await getTaskById(id);
      if (task == null) {
        throw AppDatabaseException('Task not found');
      }

      final updatedTask = task.copyWith(
        status: AppConstants.taskStatusCompleted,
        completedAt: DateTime.now(),
      );

      return await updateTask(updatedTask);
    } catch (e) {
      await Logger.instance.error('Failed to mark task as completed', e);
      throw AppDatabaseException('Failed to mark task as completed: $e');
    }
  }

  /// Get task statistics
  Future<Map<String, int>> getTaskStatistics({int? userId}) async {
    try {
      await Logger.instance.logDatabase(
        'READ',
        table: 'tasks',
        details: 'Fetching task statistics',
      );

      final db = await _database;
      String whereClause = userId != null ? 'WHERE user_id = ?' : '';
      List<dynamic> whereArgs = userId != null ? [userId] : [];

      final result = await db.rawQuery('''
        SELECT 
          status,
          COUNT(*) as count
        FROM tasks 
        $whereClause
        GROUP BY status
      ''', whereArgs);

      Map<String, int> stats = {
        AppConstants.taskStatusPending: 0,
        AppConstants.taskStatusInProgress: 0,
        AppConstants.taskStatusCompleted: 0,
      };

      for (final row in result) {
        stats[row['status'] as String] = row['count'] as int;
      }

      await Logger.instance.info('Task statistics retrieved: $stats');
      return stats;
    } catch (e) {
      await Logger.instance.error('Failed to get task statistics', e);
      throw AppDatabaseException('Failed to get task statistics: $e');
    }
  }

  /// Search tasks by title or description
  Future<List<Task>> searchTasks(String query, {int? userId}) async {
    try {
      await Logger.instance.logDatabase(
        'READ',
        table: 'tasks',
        details: 'Searching tasks with query: $query',
      );

      final db = await _database;
      String whereClause = 'WHERE (title LIKE ? OR description LIKE ?)';
      List<dynamic> whereArgs = ['%$query%', '%$query%'];

      if (userId != null) {
        whereClause += ' AND user_id = ?';
        whereArgs.add(userId);
      }

      final result = await db.rawQuery('''
        SELECT * FROM tasks 
        $whereClause
        ORDER BY created_at DESC
      ''', whereArgs);

      final tasks = result.map((map) => Task.fromMap(map)).toList();

      await Logger.instance.info(
        'Found ${tasks.length} tasks matching query: $query',
      );
      return tasks;
    } catch (e) {
      await Logger.instance.error('Failed to search tasks', e);
      throw AppDatabaseException('Failed to search tasks: $e');
    }
  }
}
