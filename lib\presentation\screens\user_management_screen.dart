import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/models/user.dart';
import '../providers/user_management_providers.dart';
import '../widgets/user_card.dart';
import 'add_edit_user_screen.dart';

/// User management screen for admins
class UserManagementScreen extends ConsumerStatefulWidget {
  const UserManagementScreen({super.key});

  @override
  ConsumerState<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends ConsumerState<UserManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _includeInactive = false;

  @override
  void initState() {
    super.initState();
    // Load users when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userManagementProvider.notifier).loadUsers();
      ref.read(userManagementProvider.notifier).loadStatistics();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      ref.read(userManagementProvider.notifier).loadUsers(includeInactive: _includeInactive);
    } else if (query.length >= 2) {
      ref.read(userManagementProvider.notifier).searchUsers(query);
    }
  }

  Future<void> _addNewUser() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const AddEditUserScreen(),
      ),
    );

    if (result == true) {
      // Refresh the user list
      ref.read(userManagementProvider.notifier).refresh(includeInactive: _includeInactive);
      ref.read(userManagementProvider.notifier).loadStatistics();
    }
  }

  Future<void> _editUser(User user) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => AddEditUserScreen(user: user),
      ),
    );

    if (result == true) {
      // Refresh the user list
      ref.read(userManagementProvider.notifier).refresh(includeInactive: _includeInactive);
      ref.read(userManagementProvider.notifier).loadStatistics();
    }
  }

  Future<void> _deleteUser(User user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete "${user.username}"?\n\nThis action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref.read(userManagementProvider.notifier).deleteUser(user.id!);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        ref.read(userManagementProvider.notifier).loadStatistics();
      }
    }
  }

  Future<void> _resetPassword(User user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Password'),
        content: Text('Reset password for "${user.username}"?\n\nA temporary password will be generated.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final tempPassword = await ref.read(userManagementProvider.notifier).resetUserPassword(user.id!);
      if (tempPassword != null && mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Password Reset'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Password reset for "${user.username}"'),
                const SizedBox(height: 16),
                const Text('Temporary password:'),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SelectableText(
                    tempPassword,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                const Text('Please share this with the user securely.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userManagementState = ref.watch(userManagementProvider);
    final statisticsAsync = ref.watch(userStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('User Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(userManagementProvider.notifier).refresh(includeInactive: _includeInactive);
              ref.read(userManagementProvider.notifier).loadStatistics();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Statistics Cards
          if (statisticsAsync.hasValue) ...[
            Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: _buildStatisticsCards(statisticsAsync.value!),
            ),
          ],

          // Search Bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search users...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _onSearchChanged,
            ),
          ),

          // Include Inactive Toggle
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
            child: Row(
              children: [
                Checkbox(
                  value: _includeInactive,
                  onChanged: (value) {
                    setState(() {
                      _includeInactive = value ?? false;
                    });
                    ref.read(userManagementProvider.notifier).loadUsers(includeInactive: _includeInactive);
                  },
                ),
                const Text('Include inactive users'),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // User List
          Expanded(
            child: _buildUserList(userManagementState),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewUser,
        child: const Icon(Icons.person_add),
      ),
    );
  }

  Widget _buildStatisticsCards(Map<String, int> stats) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard('Total Users', stats['total'] ?? 0, Colors.blue),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: _buildStatCard('Active Users', stats['active'] ?? 0, Colors.green),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: _buildStatCard('Admins', stats['admin'] ?? 0, Colors.orange),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, int value, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Text(
              value.toString(),
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserList(UserManagementState state) {
    if (state.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Error: ${state.error}',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ElevatedButton(
              onPressed: () => ref.read(userManagementProvider.notifier).refresh(includeInactive: _includeInactive),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No users found',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Tap the + button to add a new user',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(userManagementProvider.notifier).refresh(includeInactive: _includeInactive),
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: state.users.length,
        itemBuilder: (context, index) {
          final user = state.users[index];
          return UserCard(
            user: user,
            onTap: () => _editUser(user),
            onDelete: () => _deleteUser(user),
            onResetPassword: () => _resetPassword(user),
          );
        },
      ),
    );
  }
}
