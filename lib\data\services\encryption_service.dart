import 'dart:io';
import '../../core/utils/crypto_utils.dart';
import '../../core/utils/mock_secure_storage.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';

/// Service for managing database encryption
class EncryptionService {
  static EncryptionService? _instance;
  static EncryptionService get instance => _instance ??= EncryptionService._();
  
  EncryptionService._();

  final MockSecureStorage _secureStorage = MockSecureStorage.instance;
  String? _encryptionKey;

  /// Initialize encryption service
  Future<void> initialize() async {
    try {
      await Logger.instance.info('Initializing encryption service...');
      
      // Try to get existing encryption key
      _encryptionKey = await _secureStorage.read(key: AppConstants.encryptionKeyAlias);
      
      if (_encryptionKey == null) {
        // Generate new encryption key
        _encryptionKey = CryptoUtils.generateEncryptionKey();
        await _secureStorage.write(
          key: AppConstants.encryptionKeyAlias,
          value: _encryptionKey!,
        );
        
        await Logger.instance.info('New encryption key generated and stored');
      } else {
        await Logger.instance.info('Existing encryption key loaded');
      }
      
      await Logger.instance.info('Encryption service initialized successfully');
    } catch (e) {
      await Logger.instance.error('Failed to initialize encryption service', e);
      throw EncryptionException('Failed to initialize encryption: $e');
    }
  }

  /// Get the database encryption key
  Future<String> getDatabaseKey() async {
    if (_encryptionKey == null) {
      await initialize();
    }
    
    if (_encryptionKey == null) {
      throw EncryptionException('Encryption key not available');
    }
    
    return _encryptionKey!;
  }

  /// Regenerate encryption key (for key rotation)
  Future<String> regenerateKey() async {
    try {
      await Logger.instance.info('Regenerating encryption key...');
      
      // Generate new key
      _encryptionKey = CryptoUtils.generateEncryptionKey();
      
      // Store new key
      await _secureStorage.write(
        key: AppConstants.encryptionKeyAlias,
        value: _encryptionKey!,
      );
      
      await Logger.instance.info('Encryption key regenerated successfully');
      return _encryptionKey!;
    } catch (e) {
      await Logger.instance.error('Failed to regenerate encryption key', e);
      throw EncryptionException('Failed to regenerate key: $e');
    }
  }

  /// Check if encryption is available
  bool get isEncryptionAvailable {
    return _encryptionKey != null;
  }

  /// Validate encryption key
  Future<bool> validateKey() async {
    try {
      if (_encryptionKey == null) {
        return false;
      }
      
      // Basic validation - check if key is properly formatted
      if (_encryptionKey!.length < 32) {
        await Logger.instance.warning('Encryption key is too short');
        return false;
      }
      
      return true;
    } catch (e) {
      await Logger.instance.error('Failed to validate encryption key', e);
      return false;
    }
  }

  /// Clear encryption key (for security purposes)
  Future<void> clearKey() async {
    try {
      await Logger.instance.info('Clearing encryption key...');
      
      _encryptionKey = null;
      await _secureStorage.delete(key: AppConstants.encryptionKeyAlias);
      
      await Logger.instance.info('Encryption key cleared');
    } catch (e) {
      await Logger.instance.error('Failed to clear encryption key', e);
      throw EncryptionException('Failed to clear key: $e');
    }
  }

  /// Get encryption status information
  Future<Map<String, dynamic>> getEncryptionStatus() async {
    try {
      final hasKey = _encryptionKey != null;
      final isValid = hasKey ? await validateKey() : false;
      
      return {
        'hasKey': hasKey,
        'isValid': isValid,
        'keyLength': hasKey ? _encryptionKey!.length : 0,
        'algorithm': 'AES-256',
        'mode': 'SQLCipher',
      };
    } catch (e) {
      await Logger.instance.error('Failed to get encryption status', e);
      return {
        'hasKey': false,
        'isValid': false,
        'keyLength': 0,
        'algorithm': 'Unknown',
        'mode': 'Unknown',
        'error': e.toString(),
      };
    }
  }

  /// Backup encryption key (for admin purposes)
  Future<String?> backupKey() async {
    try {
      if (_encryptionKey == null) {
        throw EncryptionException('No encryption key available');
      }
      
      await Logger.instance.logUserAction('admin', 'BACKUP_ENCRYPTION_KEY');
      
      // In a real implementation, this would be more secure
      // For now, return the key for backup purposes
      return _encryptionKey!;
    } catch (e) {
      await Logger.instance.error('Failed to backup encryption key', e);
      throw EncryptionException('Failed to backup key: $e');
    }
  }

  /// Restore encryption key from backup
  Future<void> restoreKey(String backupKey) async {
    try {
      await Logger.instance.logUserAction('admin', 'RESTORE_ENCRYPTION_KEY');
      
      // Validate backup key
      if (backupKey.length < 32) {
        throw ValidationException('Invalid backup key format');
      }
      
      _encryptionKey = backupKey;
      await _secureStorage.write(
        key: AppConstants.encryptionKeyAlias,
        value: _encryptionKey!,
      );
      
      await Logger.instance.info('Encryption key restored from backup');
    } catch (e) {
      await Logger.instance.error('Failed to restore encryption key', e);
      throw EncryptionException('Failed to restore key: $e');
    }
  }

  /// Check if database file is encrypted
  Future<bool> isDatabaseEncrypted(String databasePath) async {
    try {
      final file = File(databasePath);
      if (!await file.exists()) {
        return false;
      }
      
      // Read first few bytes to check for SQLite header
      final bytes = await file.openRead(0, 16).first;
      final header = String.fromCharCodes(bytes.take(6));
      
      // SQLite databases start with "SQLite"
      // Encrypted databases won't have this readable header
      return header != 'SQLite';
    } catch (e) {
      await Logger.instance.error('Failed to check database encryption', e);
      return false;
    }
  }

  /// Get encryption metrics for monitoring
  Future<Map<String, dynamic>> getEncryptionMetrics() async {
    try {
      final status = await getEncryptionStatus();
      
      return {
        ...status,
        'lastKeyGeneration': await _secureStorage.read(key: 'last_key_generation'),
        'keyRotationCount': await _secureStorage.read(key: 'key_rotation_count') ?? '0',
        'encryptionEnabled': status['hasKey'] && status['isValid'],
      };
    } catch (e) {
      await Logger.instance.error('Failed to get encryption metrics', e);
      return {
        'error': e.toString(),
        'encryptionEnabled': false,
      };
    }
  }
}
