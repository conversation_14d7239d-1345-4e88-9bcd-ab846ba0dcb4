import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:task_manager/data/database/database_helper.dart';
import 'package:task_manager/data/repositories/task_repository.dart';
import 'package:task_manager/data/services/task_service.dart';
import 'package:task_manager/core/models/task.dart';

void main() {
  // Initialize FFI and Flutter binding for testing
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  group('Database Tests', () {
    late DatabaseHelper databaseHelper;
    late TaskRepository taskRepository;
    late TaskService taskService;

    setUp(() async {
      databaseHelper = DatabaseHelper.instance;
      taskRepository = TaskRepository.instance;
      taskService = TaskService.instance;

      // Clean up any existing database
      await databaseHelper.deleteDatabase();
    });

    tearDown(() async {
      await databaseHelper.close();
    });

    test('Database initialization should work', () async {
      final db = await databaseHelper.database;
      expect(db, isNotNull);
      expect(await databaseHelper.databaseExists(), isTrue);
    });

    test('Create and retrieve task should work', () async {
      // Create a test task
      final task = await taskService.createTask(
        title: 'Test Task',
        description: 'This is a test task',
        dueDate: DateTime.now().add(const Duration(days: 1)),
      );

      expect(task.id, isNotNull);
      expect(task.title, equals('Test Task'));
      expect(task.description, equals('This is a test task'));
      expect(task.status, equals('pending'));

      // Retrieve the task
      final retrievedTask = await taskRepository.getTaskById(task.id!);
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.title, equals('Test Task'));
    });

    test('Update task should work', () async {
      // Create a task
      final task = await taskService.createTask(
        title: 'Original Title',
        description: 'Original Description',
      );

      // Update the task
      final updatedTask = await taskService.updateTask(
        id: task.id!,
        title: 'Updated Title',
        description: 'Updated Description',
        status: 'completed',
      );

      expect(updatedTask.title, equals('Updated Title'));
      expect(updatedTask.description, equals('Updated Description'));
      expect(updatedTask.status, equals('completed'));
    });

    test('Delete task should work', () async {
      // Create a task
      final task = await taskService.createTask(
        title: 'Task to Delete',
        description: 'This task will be deleted',
      );

      // Verify task exists
      final existingTask = await taskRepository.getTaskById(task.id!);
      expect(existingTask, isNotNull);

      // Delete the task
      final deleted = await taskService.deleteTask(task.id!);
      expect(deleted, isTrue);

      // Verify task no longer exists
      final deletedTask = await taskRepository.getTaskById(task.id!);
      expect(deletedTask, isNull);
    });

    test('Search tasks should work', () async {
      // Create multiple tasks
      await taskService.createTask(
        title: 'Flutter Development',
        description: 'Working on Flutter app',
      );

      await taskService.createTask(
        title: 'Database Design',
        description: 'Designing the database schema',
      );

      await taskService.createTask(
        title: 'UI Testing',
        description: 'Testing the user interface',
      );

      // Search for tasks containing "Flutter"
      final flutterTasks = await taskService.searchTasks('Flutter');
      expect(flutterTasks.length, equals(1));
      expect(flutterTasks.first.title, equals('Flutter Development'));

      // Search for tasks containing "Testing"
      final testingTasks = await taskService.searchTasks('Testing');
      expect(testingTasks.length, equals(1));
      expect(testingTasks.first.title, equals('UI Testing'));
    });

    test('Task statistics should work', () async {
      // Create tasks with different statuses
      await taskService.createTask(title: 'Pending Task 1', status: 'pending');

      await taskService.createTask(title: 'Pending Task 2', status: 'pending');

      await taskService.createTask(
        title: 'Completed Task',
        status: 'completed',
      );

      // Get statistics
      final stats = await taskService.getTaskStatistics();
      expect(stats['pending'], equals(2));
      expect(stats['completed'], equals(1));
      expect(stats['in_progress'], equals(0));
    });
  });
}
