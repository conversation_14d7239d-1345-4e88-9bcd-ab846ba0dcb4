import 'package:flutter/material.dart';
import '../../core/design_system/app_design_system.dart';
import 'modern_components.dart';

/// Advanced ERP Data Table
/// Professional data table with sorting, filtering, pagination, and actions
class ERPDataTable<T> extends StatefulWidget {
  final List<ERPDataColumn<T>> columns;
  final List<T> data;
  final Function(T)? onRowTap;
  final Function(T)? onEdit;
  final Function(T)? onDelete;
  final Function(List<T>)? onBulkAction;
  final bool showCheckboxes;
  final bool showActions;
  final int itemsPerPage;
  final String? searchHint;
  final Widget? emptyState;

  const ERPDataTable({
    super.key,
    required this.columns,
    required this.data,
    this.onRowTap,
    this.onEdit,
    this.onDelete,
    this.onBulkAction,
    this.showCheckboxes = false,
    this.showActions = true,
    this.itemsPerPage = 10,
    this.searchHint,
    this.emptyState,
  });

  @override
  State<ERPDataTable<T>> createState() => _ERPDataTableState<T>();
}

class _ERPDataTableState<T> extends State<ERPDataTable<T>> {
  int _currentPage = 0;
  String _searchQuery = '';
  String? _sortColumn;
  bool _sortAscending = true;
  Set<T> _selectedItems = {};
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    final filteredData = _getFilteredData();
    final paginatedData = _getPaginatedData(filteredData);

    return ModernCard(
      padding: EdgeInsets.zero,
      child: Column(
        children: [
          _buildTableHeader(theme, spacing, filteredData.length),
          _buildDataTable(theme, spacing, paginatedData),
          if (filteredData.length > widget.itemsPerPage)
            _buildPagination(theme, spacing, filteredData.length),
        ],
      ),
    );
  }

  Widget _buildTableHeader(ThemeData theme, dynamic spacing, int totalItems) {
    return Container(
      padding: EdgeInsets.all(spacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDesignSystem.borderRadius.medium),
          topRight: Radius.circular(AppDesignSystem.borderRadius.medium),
        ),
      ),
      child: Row(
        children: [
          // Search
          Expanded(
            flex: 2,
            child: TextField(
              controller: _searchController,
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: widget.searchHint ?? 'Search...',
                prefixIcon: const Icon(Icons.search, size: 20),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, size: 20),
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchQuery = '');
                        },
                      )
                    : null,
                filled: true,
                fillColor: theme.colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                  borderSide: BorderSide.none,
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: spacing.md,
                  vertical: spacing.sm,
                ),
              ),
            ),
          ),
          
          SizedBox(width: spacing.md),
          
          // Results count
          Text(
            '$totalItems items',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          
          SizedBox(width: spacing.md),
          
          // Bulk actions
          if (widget.showCheckboxes && _selectedItems.isNotEmpty) ...[
            ModernButton(
              text: 'Actions (${_selectedItems.length})',
              variant: ModernButtonVariant.secondary,
              size: ModernButtonSize.small,
              onPressed: () => _showBulkActions(),
            ),
            SizedBox(width: spacing.sm),
          ],
          
          // Refresh
          IconButton(
            onPressed: () => setState(() {}),
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
          
          // Export
          IconButton(
            onPressed: () => _exportData(),
            icon: const Icon(Icons.download),
            tooltip: 'Export',
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable(ThemeData theme, dynamic spacing, List<T> data) {
    if (data.isEmpty) {
      return Container(
        height: 200,
        child: widget.emptyState ?? _buildDefaultEmptyState(theme, spacing),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        showCheckboxColumn: widget.showCheckboxes,
        sortColumnIndex: _sortColumn != null 
            ? widget.columns.indexWhere((col) => col.key == _sortColumn)
            : null,
        sortAscending: _sortAscending,
        columns: [
          ...widget.columns.map((column) => DataColumn(
            label: Text(
              column.title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            onSort: column.sortable ? (columnIndex, ascending) {
              setState(() {
                _sortColumn = column.key;
                _sortAscending = ascending;
              });
            } : null,
          )),
          if (widget.showActions)
            DataColumn(
              label: Text(
                'Actions',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
        rows: data.map((item) => DataRow(
          selected: _selectedItems.contains(item),
          onSelectChanged: widget.showCheckboxes ? (selected) {
            setState(() {
              if (selected == true) {
                _selectedItems.add(item);
              } else {
                _selectedItems.remove(item);
              }
            });
          } : null,
          cells: [
            ...widget.columns.map((column) => DataCell(
              column.cellBuilder(item),
              onTap: widget.onRowTap != null ? () => widget.onRowTap!(item) : null,
            )),
            if (widget.showActions)
              DataCell(
                _buildActionButtons(item, theme, spacing),
              ),
          ],
        )).toList(),
      ),
    );
  }

  Widget _buildActionButtons(T item, ThemeData theme, dynamic spacing) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.onEdit != null)
          IconButton(
            onPressed: () => widget.onEdit!(item),
            icon: const Icon(Icons.edit, size: 18),
            tooltip: 'Edit',
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.primaryContainer,
              foregroundColor: theme.colorScheme.onPrimaryContainer,
            ),
          ),
        if (widget.onEdit != null && widget.onDelete != null)
          SizedBox(width: spacing.xs),
        if (widget.onDelete != null)
          IconButton(
            onPressed: () => _confirmDelete(item),
            icon: const Icon(Icons.delete, size: 18),
            tooltip: 'Delete',
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.errorContainer,
              foregroundColor: theme.colorScheme.onErrorContainer,
            ),
          ),
      ],
    );
  }

  Widget _buildPagination(ThemeData theme, dynamic spacing, int totalItems) {
    final totalPages = (totalItems / widget.itemsPerPage).ceil();
    
    return Container(
      padding: EdgeInsets.all(spacing.lg),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Showing ${_currentPage * widget.itemsPerPage + 1}-${(_currentPage + 1) * widget.itemsPerPage > totalItems ? totalItems : (_currentPage + 1) * widget.itemsPerPage} of $totalItems',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: _currentPage > 0 ? () => setState(() => _currentPage--) : null,
            icon: const Icon(Icons.chevron_left),
          ),
          ...List.generate(
            totalPages > 5 ? 5 : totalPages,
            (index) {
              final pageIndex = totalPages > 5 
                  ? (_currentPage < 3 ? index : _currentPage - 2 + index)
                  : index;
              
              if (pageIndex >= totalPages) return const SizedBox();
              
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: spacing.xs),
                child: InkWell(
                  onTap: () => setState(() => _currentPage = pageIndex),
                  borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: _currentPage == pageIndex 
                          ? theme.colorScheme.primary
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                    ),
                    child: Center(
                      child: Text(
                        '${pageIndex + 1}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: _currentPage == pageIndex
                              ? theme.colorScheme.onPrimary
                              : theme.colorScheme.onSurface,
                          fontWeight: _currentPage == pageIndex 
                              ? FontWeight.w600 
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
          IconButton(
            onPressed: _currentPage < totalPages - 1 ? () => setState(() => _currentPage++) : null,
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultEmptyState(ThemeData theme, dynamic spacing) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox,
            size: 64,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: spacing.md),
          Text(
            'No data available',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: spacing.sm),
          Text(
            _searchQuery.isNotEmpty 
                ? 'No results found for "$_searchQuery"'
                : 'There are no items to display',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  List<T> _getFilteredData() {
    if (_searchQuery.isEmpty) return widget.data;
    
    return widget.data.where((item) {
      return widget.columns.any((column) {
        final cellWidget = column.cellBuilder(item);
        if (cellWidget is Text) {
          return cellWidget.data?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false;
        }
        return false;
      });
    }).toList();
  }

  List<T> _getPaginatedData(List<T> data) {
    final startIndex = _currentPage * widget.itemsPerPage;
    final endIndex = (startIndex + widget.itemsPerPage).clamp(0, data.length);
    return data.sublist(startIndex, endIndex);
  }

  void _showBulkActions() {
    // TODO: Implement bulk actions menu
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Bulk actions for ${_selectedItems.length} items'),
      ),
    );
  }

  void _exportData() {
    // TODO: Implement data export
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality - Coming soon'),
      ),
    );
  }

  void _confirmDelete(T item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this item?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onDelete!(item);
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

/// Data column definition for ERP table
class ERPDataColumn<T> {
  final String key;
  final String title;
  final Widget Function(T) cellBuilder;
  final bool sortable;

  const ERPDataColumn({
    required this.key,
    required this.title,
    required this.cellBuilder,
    this.sortable = true,
  });
}
