import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../core/models/task.dart';
import '../../core/constants/app_constants.dart';

/// Widget for displaying a task in a card format
class TaskCard extends StatelessWidget {
  final Task task;
  final VoidCallback? onTap;
  final VoidCallback? onStatusToggle;
  final VoidCallback? onDelete;

  const TaskCard({
    super.key,
    required this.task,
    this.onTap,
    this.onStatusToggle,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isCompleted = task.status == AppConstants.taskStatusCompleted;
    final isOverdue = task.isOverdue;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Status Checkbox
                  Checkbox(
                    value: isCompleted,
                    onChanged: onStatusToggle != null ? (_) => onStatusToggle!() : null,
                  ),
                  
                  // Title and Status
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          task.getTruncatedTitle(40),
                          style: theme.textTheme.titleMedium?.copyWith(
                            decoration: isCompleted ? TextDecoration.lineThrough : null,
                            color: isCompleted 
                                ? theme.colorScheme.onSurface.withOpacity(0.6)
                                : null,
                          ),
                        ),
                        const SizedBox(height: 4),
                        _buildStatusChip(context),
                      ],
                    ),
                  ),
                  
                  // Actions
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onTap?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                        case 'toggle':
                          onStatusToggle?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'toggle',
                        child: Row(
                          children: [
                            Icon(isCompleted ? Icons.undo : Icons.check),
                            const SizedBox(width: 8),
                            Text(isCompleted ? 'Mark Pending' : 'Mark Complete'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // Description
              if (task.description != null && task.description!.isNotEmpty) ...[
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  task.description!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // Footer with dates and indicators
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                children: [
                  // Created date
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Created ${_formatDate(task.createdAt)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // Due date or completion indicator
                  if (task.dueDate != null) ...[
                    Icon(
                      isOverdue ? Icons.warning : Icons.event,
                      size: 16,
                      color: isOverdue ? Colors.red : theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Due ${_formatDate(task.dueDate!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isOverdue ? Colors.red : theme.colorScheme.primary,
                        fontWeight: isOverdue ? FontWeight.bold : null,
                      ),
                    ),
                  ] else if (isCompleted && task.completedAt != null) ...[
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Completed ${_formatDate(task.completedAt!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color chipColor;
    String statusText;

    switch (task.status) {
      case AppConstants.taskStatusCompleted:
        chipColor = Colors.green;
        statusText = 'Completed';
        break;
      case AppConstants.taskStatusInProgress:
        chipColor = Colors.orange;
        statusText = 'In Progress';
        break;
      case AppConstants.taskStatusPending:
      default:
        chipColor = Colors.grey;
        statusText = 'Pending';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Text(
        statusText,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: chipColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('MMM d, y').format(date);
    }
  }
}
