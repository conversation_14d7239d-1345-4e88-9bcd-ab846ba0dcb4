import '../../core/utils/mock_secure_storage.dart';
import '../../core/models/user.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/utils/crypto_utils.dart';
import '../../core/constants/app_constants.dart';
import '../repositories/user_repository.dart';

/// Service class for handling user authentication
class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  final UserRepository _userRepository = UserRepository.instance;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    wOptions: WindowsOptions(),
    lOptions: LinuxOptions(),
  );

  User? _currentUser;
  String? _currentSessionToken;

  /// Get the currently logged-in user
  User? get currentUser => _currentUser;

  /// Check if user is logged in
  bool get isLoggedIn => _currentUser != null && _currentSessionToken != null;

  /// Check if current user is admin
  bool get isAdmin => _currentUser?.role == AppConstants.adminRole;

  /// Initialize authentication service
  Future<void> initialize() async {
    try {
      await Logger.instance.info('Initializing authentication service...');

      // Check for existing session
      await _checkExistingSession();

      await Logger.instance.info('Authentication service initialized');
    } catch (e) {
      await Logger.instance.error('Failed to initialize auth service', e);
    }
  }

  /// Check for existing session and auto-login if enabled
  Future<void> _checkExistingSession() async {
    try {
      final autoLoginEnabled = await _secureStorage.read(
        key: AppConstants.autoLoginKey,
      );
      if (autoLoginEnabled != 'true') {
        await Logger.instance.info('Auto-login disabled');
        return;
      }

      final sessionToken = await _secureStorage.read(
        key: AppConstants.userSessionKey,
      );
      if (sessionToken == null) {
        await Logger.instance.info('No existing session found');
        return;
      }

      // Validate session token
      if (!CryptoUtils.isValidSessionToken(sessionToken)) {
        await Logger.instance.warning('Invalid session token format');
        await _clearSession();
        return;
      }

      // Check if session is expired (24 hours)
      if (CryptoUtils.isSessionTokenExpired(
        sessionToken,
        const Duration(hours: 24),
      )) {
        await Logger.instance.info('Session token expired');
        await _clearSession();
        return;
      }

      // Get user ID from session
      final userIdStr = await _secureStorage.read(key: 'user_id');
      if (userIdStr == null) {
        await Logger.instance.warning('No user ID found in session');
        await _clearSession();
        return;
      }

      final userId = int.tryParse(userIdStr);
      if (userId == null) {
        await Logger.instance.warning('Invalid user ID in session');
        await _clearSession();
        return;
      }

      // Load user from database
      final user = await _userRepository.getUserById(userId);
      if (user == null || !user.isActive) {
        await Logger.instance.warning('User not found or inactive');
        await _clearSession();
        return;
      }

      // Restore session
      _currentUser = user;
      _currentSessionToken = sessionToken;

      await Logger.instance.logAuth(user.username, 'AUTO_LOGIN', success: true);
      await Logger.instance.info('Session restored for user: ${user.username}');
    } catch (e) {
      await Logger.instance.error('Failed to check existing session', e);
      await _clearSession();
    }
  }

  /// Login with username and password
  Future<User> login({
    required String username,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      await Logger.instance.logAuth(username, 'LOGIN_ATTEMPT');

      // Validate input
      if (username.trim().isEmpty || password.trim().isEmpty) {
        throw ValidationException('Username and password are required');
      }

      // Check for admin credentials first
      if (username.trim() == AppConstants.adminUsername &&
          password.trim() == AppConstants.adminPassword) {
        // Get or create admin user
        User? adminUser = await _userRepository.getUserByUsername(
          AppConstants.adminUsername,
        );

        if (adminUser == null) {
          // Create admin user if doesn't exist
          adminUser = await _createAdminUser();
        }

        return await _completeLogin(adminUser, rememberMe);
      }

      // Regular user login
      final user = await _userRepository.getUserByUsername(username.trim());
      if (user == null) {
        await Logger.instance.logAuth(username, 'LOGIN_FAILED', success: false);
        throw AuthenticationException('Invalid username or password');
      }

      if (!user.isActive) {
        await Logger.instance.logAuth(
          username,
          'LOGIN_FAILED_INACTIVE',
          success: false,
        );
        throw AuthenticationException('Account is disabled');
      }

      // Verify password
      if (!CryptoUtils.verifyPassword(
        password,
        user.passwordHash,
        _extractSalt(user.passwordHash),
      )) {
        await Logger.instance.logAuth(username, 'LOGIN_FAILED', success: false);
        throw AuthenticationException('Invalid username or password');
      }

      return await _completeLogin(user, rememberMe);
    } catch (e) {
      await Logger.instance.error('Login failed for user: $username', e);
      rethrow;
    }
  }

  /// Complete the login process
  Future<User> _completeLogin(User user, bool rememberMe) async {
    try {
      // Generate session token
      _currentSessionToken = CryptoUtils.generateSessionToken();
      _currentUser = user;

      // Update last login time
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());
      await _userRepository.updateUser(updatedUser);
      _currentUser = updatedUser;

      // Store session if remember me is enabled
      if (rememberMe) {
        await _secureStorage.write(
          key: AppConstants.autoLoginKey,
          value: 'true',
        );
        await _secureStorage.write(
          key: AppConstants.userSessionKey,
          value: _currentSessionToken!,
        );
        await _secureStorage.write(key: 'user_id', value: user.id.toString());
      } else {
        await _secureStorage.write(
          key: AppConstants.autoLoginKey,
          value: 'false',
        );
      }

      await Logger.instance.logAuth(
        user.username,
        'LOGIN_SUCCESS',
        success: true,
      );
      await Logger.instance.info(
        'User logged in successfully: ${user.username}',
      );

      return updatedUser;
    } catch (e) {
      await Logger.instance.error('Failed to complete login', e);
      throw AuthenticationException('Login failed: $e');
    }
  }

  /// Create admin user
  Future<User> _createAdminUser() async {
    try {
      await Logger.instance.info('Creating admin user...');

      final salt = CryptoUtils.generateSalt();
      final passwordHash = CryptoUtils.hashPassword(
        AppConstants.adminPassword,
        salt,
      );
      final hashedPasswordWithSalt = '$passwordHash:$salt';

      final adminUser = User(
        username: AppConstants.adminUsername,
        passwordHash: hashedPasswordWithSalt,
        role: AppConstants.adminRole,
        createdAt: DateTime.now(),
        isActive: true,
      );

      final createdUser = await _userRepository.createUser(adminUser);
      await Logger.instance.info('Admin user created successfully');

      return createdUser;
    } catch (e) {
      await Logger.instance.error('Failed to create admin user', e);
      throw AuthenticationException('Failed to create admin user: $e');
    }
  }

  /// Extract salt from stored password hash
  String _extractSalt(String storedHash) {
    final parts = storedHash.split(':');
    return parts.length > 1 ? parts[1] : '';
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      if (_currentUser != null) {
        await Logger.instance.logAuth(_currentUser!.username, 'LOGOUT');
        await Logger.instance.info(
          'User logged out: ${_currentUser!.username}',
        );
      }

      await _clearSession();

      _currentUser = null;
      _currentSessionToken = null;
    } catch (e) {
      await Logger.instance.error('Failed to logout', e);
      throw AuthenticationException('Logout failed: $e');
    }
  }

  /// Clear stored session data
  Future<void> _clearSession() async {
    try {
      await _secureStorage.delete(key: AppConstants.userSessionKey);
      await _secureStorage.delete(key: 'user_id');
      await _secureStorage.delete(key: AppConstants.autoLoginKey);
    } catch (e) {
      await Logger.instance.error('Failed to clear session', e);
    }
  }

  /// Change password for current user
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        throw AuthenticationException('No user logged in');
      }

      // Validate current password
      if (!CryptoUtils.verifyPassword(
        currentPassword,
        _currentUser!.passwordHash,
        _extractSalt(_currentUser!.passwordHash),
      )) {
        throw AuthenticationException('Current password is incorrect');
      }

      // Validate new password
      if (newPassword.length < 6) {
        throw ValidationException('New password must be at least 6 characters');
      }

      // Hash new password
      final salt = CryptoUtils.generateSalt();
      final passwordHash = CryptoUtils.hashPassword(newPassword, salt);
      final hashedPasswordWithSalt = '$passwordHash:$salt';

      // Update user
      final updatedUser = _currentUser!.copyWith(
        passwordHash: hashedPasswordWithSalt,
      );
      await _userRepository.updateUser(updatedUser);
      _currentUser = updatedUser;

      await Logger.instance.logUserAction(
        _currentUser!.username,
        'PASSWORD_CHANGED',
      );
      await Logger.instance.info(
        'Password changed for user: ${_currentUser!.username}',
      );
    } catch (e) {
      await Logger.instance.error('Failed to change password', e);
      rethrow;
    }
  }

  /// Check if auto-login is enabled
  Future<bool> isAutoLoginEnabled() async {
    try {
      final value = await _secureStorage.read(key: AppConstants.autoLoginKey);
      return value == 'true';
    } catch (e) {
      await Logger.instance.error('Failed to check auto-login status', e);
      return false;
    }
  }

  /// Enable or disable auto-login
  Future<void> setAutoLogin(bool enabled) async {
    try {
      await _secureStorage.write(
        key: AppConstants.autoLoginKey,
        value: enabled ? 'true' : 'false',
      );

      if (!enabled) {
        // Clear session data if auto-login is disabled
        await _secureStorage.delete(key: AppConstants.userSessionKey);
        await _secureStorage.delete(key: 'user_id');
      }

      await Logger.instance.info(
        'Auto-login ${enabled ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      await Logger.instance.error('Failed to set auto-login', e);
      throw AuthenticationException('Failed to update auto-login setting: $e');
    }
  }
}
