import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/design_system/app_design_system.dart';
import '../../core/constants/erp_modules.dart';
import '../../core/constants/app_constants.dart';
import '../providers/auth_providers.dart';

/// Modern ERP Navigation Sidebar
/// Comprehensive navigation system for enterprise ERP application
class ERPNavigation extends ConsumerStatefulWidget {
  final String currentModule;
  final Function(String) onModuleSelected;

  const ERPNavigation({
    super.key,
    required this.currentModule,
    required this.onModuleSelected,
  });

  @override
  ConsumerState<ERPNavigation> createState() => _ERPNavigationState();
}

class _ERPNavigationState extends ConsumerState<ERPNavigation> {
  String _searchQuery = '';
  String? _expandedCategory;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    final user = ref.watch(currentUserProvider);

    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(theme, spacing, user),
          _buildSearchBar(theme, spacing),
          Expanded(
            child: _buildModuleList(theme, spacing),
          ),
          _buildFooter(theme, spacing),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, dynamic spacing, dynamic user) {
    return Container(
      padding: EdgeInsets.all(spacing.lg),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(spacing.sm),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                ),
                child: Icon(
                  Icons.business,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              SizedBox(width: spacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ERP System',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Enterprise Resource Planning',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: spacing.md),
          Container(
            padding: EdgeInsets.all(spacing.sm),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  child: Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                SizedBox(width: spacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user?.username ?? 'User',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        user?.role ?? 'Role',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(ThemeData theme, dynamic spacing) {
    return Container(
      padding: EdgeInsets.all(spacing.md),
      child: TextField(
        controller: _searchController,
        onChanged: (value) => setState(() => _searchQuery = value),
        decoration: InputDecoration(
          hintText: 'Search modules...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() => _searchQuery = '');
                  },
                )
              : null,
          filled: true,
          fillColor: theme.colorScheme.surfaceContainerHighest,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
            borderSide: BorderSide.none,
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: spacing.md,
            vertical: spacing.sm,
          ),
        ),
      ),
    );
  }

  Widget _buildModuleList(ThemeData theme, dynamic spacing) {
    final allModules = ERPModules.getAllModules();
    final filteredModules = _filterModules(allModules);

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: spacing.sm),
      itemCount: filteredModules.length,
      itemBuilder: (context, index) {
        final category = filteredModules.keys.elementAt(index);
        final modules = filteredModules[category]!;
        
        return _buildModuleCategory(category, modules, theme, spacing);
      },
    );
  }

  Widget _buildModuleCategory(
    String category,
    List<ERPModule> modules,
    ThemeData theme,
    dynamic spacing,
  ) {
    final isExpanded = _expandedCategory == category;
    
    return Column(
      children: [
        InkWell(
          onTap: () => setState(() {
            _expandedCategory = isExpanded ? null : category;
          }),
          borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: spacing.md,
              vertical: spacing.sm,
            ),
            child: Row(
              children: [
                Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                SizedBox(width: spacing.sm),
                Expanded(
                  child: Text(
                    category,
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: spacing.xs,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
                  ),
                  child: Text(
                    '${modules.length}',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isExpanded) ...[
          ...modules.map((module) => _buildModuleItem(module, theme, spacing)),
          SizedBox(height: spacing.sm),
        ],
      ],
    );
  }

  Widget _buildModuleItem(ERPModule module, ThemeData theme, dynamic spacing) {
    final isSelected = widget.currentModule == module.id;
    
    return Container(
      margin: EdgeInsets.only(
        left: spacing.lg,
        right: spacing.xs,
        bottom: spacing.xs,
      ),
      child: InkWell(
        onTap: () => widget.onModuleSelected(module.id),
        borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: spacing.md,
            vertical: spacing.sm,
          ),
          decoration: BoxDecoration(
            color: isSelected 
                ? theme.colorScheme.primaryContainer
                : Colors.transparent,
            borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
            border: isSelected
                ? Border.all(color: theme.colorScheme.primary.withValues(alpha: 0.3))
                : null,
          ),
          child: Row(
            children: [
              Icon(
                module.icon,
                size: 20,
                color: isSelected
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: spacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      module.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? theme.colorScheme.onPrimaryContainer
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                    if (module.description.isNotEmpty)
                      Text(
                        module.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isSelected
                              ? theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.7)
                              : theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.chevron_right,
                  size: 16,
                  color: theme.colorScheme.onPrimaryContainer,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(ThemeData theme, dynamic spacing) {
    return Container(
      padding: EdgeInsets.all(spacing.md),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => widget.onModuleSelected(ERPModules.settings),
            borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: spacing.md,
                vertical: spacing.sm,
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.settings,
                    size: 20,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: spacing.sm),
                  Text(
                    'Settings',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: spacing.xs),
          InkWell(
            onTap: () => ref.read(authProvider.notifier).logout(),
            borderRadius: BorderRadius.circular(AppDesignSystem.borderRadius.small),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: spacing.md,
                vertical: spacing.sm,
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.logout,
                    size: 20,
                    color: theme.colorScheme.error,
                  ),
                  SizedBox(width: spacing.sm),
                  Text(
                    'Logout',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, List<ERPModule>> _filterModules(Map<String, List<ERPModule>> allModules) {
    if (_searchQuery.isEmpty) return allModules;

    final filtered = <String, List<ERPModule>>{};
    
    for (final entry in allModules.entries) {
      final filteredModules = entry.value.where((module) {
        return module.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               module.description.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
      
      if (filteredModules.isNotEmpty) {
        filtered[entry.key] = filteredModules;
      }
    }
    
    return filtered;
  }
}
