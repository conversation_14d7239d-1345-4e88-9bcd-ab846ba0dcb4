import 'package:flutter/material.dart';
import '../../core/design_system/app_design_system.dart';

/// Modern Button Component
class ModernButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final ModernButtonVariant variant;
  final ModernButtonSize size;
  final bool isLoading;
  final bool isFullWidth;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.variant = ModernButtonVariant.primary,
    this.size = ModernButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    
    Widget button;
    
    switch (variant) {
      case ModernButtonVariant.primary:
        button = ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            padding: _getPadding(),
            minimumSize: isFullWidth ? const Size(double.infinity, 0) : null,
          ),
          child: _buildContent(theme),
        );
        break;
      case ModernButtonVariant.secondary:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            padding: _getPadding(),
            minimumSize: isFullWidth ? const Size(double.infinity, 0) : null,
          ),
          child: _buildContent(theme),
        );
        break;
      case ModernButtonVariant.ghost:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            padding: _getPadding(),
            minimumSize: isFullWidth ? const Size(double.infinity, 0) : null,
          ),
          child: _buildContent(theme),
        );
        break;
    }

    return button;
  }

  Widget _buildContent(ThemeData theme) {
    if (isLoading) {
      return SizedBox(
        height: _getIconSize(),
        width: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            variant == ModernButtonVariant.primary 
                ? theme.colorScheme.onPrimary 
                : theme.colorScheme.primary,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize()),
          SizedBox(width: AppDesignSystem.spacing.sm),
          Text(text),
        ],
      );
    }

    return Text(text);
  }

  EdgeInsets _getPadding() {
    final spacing = AppDesignSystem.spacing;
    switch (size) {
      case ModernButtonSize.small:
        return EdgeInsets.symmetric(horizontal: spacing.md, vertical: spacing.sm);
      case ModernButtonSize.medium:
        return EdgeInsets.symmetric(horizontal: spacing.lg, vertical: spacing.md);
      case ModernButtonSize.large:
        return EdgeInsets.symmetric(horizontal: spacing.xl, vertical: spacing.lg);
    }
  }

  double _getIconSize() {
    switch (size) {
      case ModernButtonSize.small:
        return 16;
      case ModernButtonSize.medium:
        return 20;
      case ModernButtonSize.large:
        return 24;
    }
  }
}

enum ModernButtonVariant { primary, secondary, ghost }
enum ModernButtonSize { small, medium, large }

/// Modern Input Field Component
class ModernInputField extends StatelessWidget {
  final String label;
  final String? hint;
  final String? errorText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool obscureText;
  final TextInputType keyboardType;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLines;
  final bool enabled;

  const ModernInputField({
    super.key,
    required this.label,
    this.hint,
    this.errorText,
    this.controller,
    this.onChanged,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: spacing.xs),
        TextField(
          controller: controller,
          onChanged: onChanged,
          obscureText: obscureText,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enabled: enabled,
          decoration: InputDecoration(
            hintText: hint,
            errorText: errorText,
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
            suffixIcon: suffixIcon,
          ),
        ),
      ],
    );
  }
}

/// Modern Card Component
class ModernCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final VoidCallback? onTap;
  final bool elevated;
  final Color? backgroundColor;

  const ModernCard({
    super.key,
    required this.child,
    this.padding,
    this.onTap,
    this.elevated = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final spacing = AppDesignSystem.spacing;
    final borderRadius = AppDesignSystem.borderRadius;

    Widget card = Container(
      padding: padding ?? EdgeInsets.all(spacing.lg),
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(borderRadius.medium),
        boxShadow: elevated ? AppDesignSystem.shadows.medium : null,
        border: !elevated ? Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ) : null,
      ),
      child: child,
    );

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius.medium),
        child: card,
      );
    }

    return card;
  }
}

/// Modern Chip Component
class ModernChip extends StatelessWidget {
  final String label;
  final bool selected;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;

  const ModernChip({
    super.key,
    required this.label,
    this.selected = false,
    this.onTap,
    this.backgroundColor,
    this.textColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    final borderRadius = AppDesignSystem.borderRadius;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(borderRadius.full),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: spacing.md,
          vertical: spacing.sm,
        ),
        decoration: BoxDecoration(
          color: selected 
              ? (backgroundColor ?? theme.colorScheme.primaryContainer)
              : theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(borderRadius.full),
          border: Border.all(
            color: selected 
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: 16,
                color: selected 
                    ? (textColor ?? theme.colorScheme.onPrimaryContainer)
                    : theme.colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: spacing.xs),
            ],
            Text(
              label,
              style: theme.textTheme.labelMedium?.copyWith(
                color: selected 
                    ? (textColor ?? theme.colorScheme.onPrimaryContainer)
                    : theme.colorScheme.onSurfaceVariant,
                fontWeight: selected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Modern Loading Indicator
class ModernLoadingIndicator extends StatelessWidget {
  final String? message;
  final double size;

  const ModernLoadingIndicator({
    super.key,
    this.message,
    this.size = 24,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
          ),
        ),
        if (message != null) ...[
          SizedBox(height: spacing.md),
          Text(
            message!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }
}

/// Modern Badge Component
class ModernBadge extends StatelessWidget {
  final String text;
  final Color? backgroundColor;
  final Color? textColor;
  final ModernBadgeVariant variant;

  const ModernBadge({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
    this.variant = ModernBadgeVariant.neutral,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AppDesignSystem.spacing;
    final borderRadius = AppDesignSystem.borderRadius;
    final statusColors = AppDesignSystem.statusColors;

    Color bgColor;
    Color fgColor;

    switch (variant) {
      case ModernBadgeVariant.success:
        bgColor = statusColors.success.withValues(alpha: 0.1);
        fgColor = statusColors.success;
        break;
      case ModernBadgeVariant.warning:
        bgColor = statusColors.warning.withValues(alpha: 0.1);
        fgColor = statusColors.warning;
        break;
      case ModernBadgeVariant.error:
        bgColor = statusColors.error.withValues(alpha: 0.1);
        fgColor = statusColors.error;
        break;
      case ModernBadgeVariant.info:
        bgColor = statusColors.info.withValues(alpha: 0.1);
        fgColor = statusColors.info;
        break;
      case ModernBadgeVariant.neutral:
        bgColor = theme.colorScheme.surfaceContainerHighest;
        fgColor = theme.colorScheme.onSurfaceVariant;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: spacing.sm,
        vertical: spacing.xs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? bgColor,
        borderRadius: BorderRadius.circular(borderRadius.small),
      ),
      child: Text(
        text,
        style: theme.textTheme.labelSmall?.copyWith(
          color: textColor ?? fgColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

enum ModernBadgeVariant { success, warning, error, info, neutral }
