import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/mock_secure_storage.dart';
import '../../core/utils/logger.dart';
import '../../data/services/database_stats_service.dart';

/// System settings screen for admin configuration
class SystemSettingsScreen extends ConsumerStatefulWidget {
  const SystemSettingsScreen({super.key});

  @override
  ConsumerState<SystemSettingsScreen> createState() => _SystemSettingsScreenState();
}

class _SystemSettingsScreenState extends ConsumerState<SystemSettingsScreen> {
  final MockSecureStorage _storage = MockSecureStorage.instance;
  
  // Settings state
  bool _enableAuditLogging = true;
  bool _enableAutoBackup = false;
  bool _enableSessionTimeout = true;
  bool _enablePasswordComplexity = false;
  bool _enableDataEncryption = true;
  int _sessionTimeoutMinutes = 1440; // 24 hours
  int _autoBackupDays = 7;
  int _maxLoginAttempts = 5;
  int _passwordMinLength = 6;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      // Load settings from storage
      final auditLogging = await _storage.read(key: 'system_audit_logging');
      final autoBackup = await _storage.read(key: 'system_auto_backup');
      final sessionTimeout = await _storage.read(key: 'system_session_timeout');
      final passwordComplexity = await _storage.read(key: 'system_password_complexity');
      final dataEncryption = await _storage.read(key: 'system_data_encryption');
      final sessionTimeoutMinutes = await _storage.read(key: 'system_session_timeout_minutes');
      final autoBackupDays = await _storage.read(key: 'system_auto_backup_days');
      final maxLoginAttempts = await _storage.read(key: 'system_max_login_attempts');
      final passwordMinLength = await _storage.read(key: 'system_password_min_length');

      setState(() {
        _enableAuditLogging = auditLogging == 'true';
        _enableAutoBackup = autoBackup == 'true';
        _enableSessionTimeout = sessionTimeout != 'false';
        _enablePasswordComplexity = passwordComplexity == 'true';
        _enableDataEncryption = dataEncryption != 'false';
        _sessionTimeoutMinutes = int.tryParse(sessionTimeoutMinutes ?? '1440') ?? 1440;
        _autoBackupDays = int.tryParse(autoBackupDays ?? '7') ?? 7;
        _maxLoginAttempts = int.tryParse(maxLoginAttempts ?? '5') ?? 5;
        _passwordMinLength = int.tryParse(passwordMinLength ?? '6') ?? 6;
      });
    } catch (e) {
      await Logger.instance.error('Failed to load system settings', e);
    }
  }

  Future<void> _saveSetting(String key, String value) async {
    try {
      await _storage.write(key: key, value: value);
      await Logger.instance.info('System setting saved: $key = $value');
    } catch (e) {
      await Logger.instance.error('Failed to save system setting: $key', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('System Settings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveAllSettings,
            tooltip: 'Save All Settings',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Security Settings
            _buildSectionHeader('Security Settings'),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Data Encryption'),
                    subtitle: const Text('Enable SQLCipher database encryption'),
                    value: _enableDataEncryption,
                    onChanged: (value) {
                      setState(() {
                        _enableDataEncryption = value;
                      });
                      _saveSetting('system_data_encryption', value.toString());
                    },
                  ),
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('Session Timeout'),
                    subtitle: Text('Auto-logout after $_sessionTimeoutMinutes minutes'),
                    value: _enableSessionTimeout,
                    onChanged: (value) {
                      setState(() {
                        _enableSessionTimeout = value;
                      });
                      _saveSetting('system_session_timeout', value.toString());
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    title: const Text('Session Timeout Duration'),
                    subtitle: Text('$_sessionTimeoutMinutes minutes'),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editSessionTimeout(),
                    ),
                  ),
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('Password Complexity'),
                    subtitle: const Text('Enforce strong password requirements'),
                    value: _enablePasswordComplexity,
                    onChanged: (value) {
                      setState(() {
                        _enablePasswordComplexity = value;
                      });
                      _saveSetting('system_password_complexity', value.toString());
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    title: const Text('Minimum Password Length'),
                    subtitle: Text('$_passwordMinLength characters'),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editPasswordLength(),
                    ),
                  ),
                  const Divider(height: 1),
                  ListTile(
                    title: const Text('Max Login Attempts'),
                    subtitle: Text('$_maxLoginAttempts attempts before lockout'),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editMaxLoginAttempts(),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // System Settings
            _buildSectionHeader('System Settings'),
            Card(
              child: Column(
                children: [
                  SwitchListTile(
                    title: const Text('Audit Logging'),
                    subtitle: const Text('Log all user actions and system events'),
                    value: _enableAuditLogging,
                    onChanged: (value) {
                      setState(() {
                        _enableAuditLogging = value;
                      });
                      _saveSetting('system_audit_logging', value.toString());
                    },
                  ),
                  const Divider(height: 1),
                  SwitchListTile(
                    title: const Text('Auto Backup'),
                    subtitle: Text('Automatic backup every $_autoBackupDays days'),
                    value: _enableAutoBackup,
                    onChanged: (value) {
                      setState(() {
                        _enableAutoBackup = value;
                      });
                      _saveSetting('system_auto_backup', value.toString());
                    },
                  ),
                  const Divider(height: 1),
                  ListTile(
                    title: const Text('Auto Backup Interval'),
                    subtitle: Text('Every $_autoBackupDays days'),
                    trailing: IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editAutoBackupDays(),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Database Management
            _buildSectionHeader('Database Management'),
            Card(
              child: Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.storage),
                    title: const Text('Database Statistics'),
                    subtitle: const Text('View database size and usage'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _showDatabaseStats,
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.cleaning_services),
                    title: const Text('Cleanup Old Backups'),
                    subtitle: const Text('Remove old backup files'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _cleanupOldBackups,
                  ),
                  const Divider(height: 1),
                  ListTile(
                    leading: const Icon(Icons.refresh),
                    title: const Text('Reset to Defaults'),
                    subtitle: const Text('Reset all settings to default values'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: _resetToDefaults,
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // System Information
            _buildSectionHeader('System Information'),
            Card(
              child: Column(
                children: [
                  _buildInfoTile('App Version', AppConstants.appVersion),
                  const Divider(height: 1),
                  _buildInfoTile('Database Version', AppConstants.databaseVersion.toString()),
                  const Divider(height: 1),
                  _buildInfoTile('Encryption', _enableDataEncryption ? 'Enabled (SQLCipher)' : 'Disabled'),
                  const Divider(height: 1),
                  _buildInfoTile('Audit Logging', _enableAuditLogging ? 'Enabled' : 'Disabled'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      title: Text(title),
      trailing: Text(
        value,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Future<void> _editSessionTimeout() async {
    final result = await showDialog<int>(
      context: context,
      builder: (context) => _NumberInputDialog(
        title: 'Session Timeout',
        label: 'Minutes',
        initialValue: _sessionTimeoutMinutes,
        min: 5,
        max: 10080, // 1 week
      ),
    );

    if (result != null) {
      setState(() {
        _sessionTimeoutMinutes = result;
      });
      await _saveSetting('system_session_timeout_minutes', result.toString());
    }
  }

  Future<void> _editPasswordLength() async {
    final result = await showDialog<int>(
      context: context,
      builder: (context) => _NumberInputDialog(
        title: 'Minimum Password Length',
        label: 'Characters',
        initialValue: _passwordMinLength,
        min: 4,
        max: 50,
      ),
    );

    if (result != null) {
      setState(() {
        _passwordMinLength = result;
      });
      await _saveSetting('system_password_min_length', result.toString());
    }
  }

  Future<void> _editMaxLoginAttempts() async {
    final result = await showDialog<int>(
      context: context,
      builder: (context) => _NumberInputDialog(
        title: 'Max Login Attempts',
        label: 'Attempts',
        initialValue: _maxLoginAttempts,
        min: 1,
        max: 20,
      ),
    );

    if (result != null) {
      setState(() {
        _maxLoginAttempts = result;
      });
      await _saveSetting('system_max_login_attempts', result.toString());
    }
  }

  Future<void> _editAutoBackupDays() async {
    final result = await showDialog<int>(
      context: context,
      builder: (context) => _NumberInputDialog(
        title: 'Auto Backup Interval',
        label: 'Days',
        initialValue: _autoBackupDays,
        min: 1,
        max: 365,
      ),
    );

    if (result != null) {
      setState(() {
        _autoBackupDays = result;
      });
      await _saveSetting('system_auto_backup_days', result.toString());
    }
  }

  Future<void> _saveAllSettings() async {
    try {
      await _saveSetting('system_audit_logging', _enableAuditLogging.toString());
      await _saveSetting('system_auto_backup', _enableAutoBackup.toString());
      await _saveSetting('system_session_timeout', _enableSessionTimeout.toString());
      await _saveSetting('system_password_complexity', _enablePasswordComplexity.toString());
      await _saveSetting('system_data_encryption', _enableDataEncryption.toString());
      await _saveSetting('system_session_timeout_minutes', _sessionTimeoutMinutes.toString());
      await _saveSetting('system_auto_backup_days', _autoBackupDays.toString());
      await _saveSetting('system_max_login_attempts', _maxLoginAttempts.toString());
      await _saveSetting('system_password_min_length', _passwordMinLength.toString());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All settings saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showDatabaseStats() async {
    try {
      final stats = await DatabaseStatsService.instance.getDatabaseStatistics();
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Database Statistics'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Total Size: ${stats['database_size']['formatted_size']}'),
                Text('Total Tasks: ${stats['record_counts']['total_tasks']}'),
                Text('Total Users: ${stats['record_counts']['total_users']}'),
                Text('Active Users: ${stats['record_counts']['active_users']}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load database statistics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cleanupOldBackups() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cleanup Old Backups'),
        content: const Text('This will delete old backup files, keeping only the 10 most recent. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Cleanup'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final result = await DatabaseStatsService.instance.cleanupOldBackups();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Cleaned up ${result['deleted_count']} files, freed ${result['freed_size']}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to cleanup backups: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset to Defaults'),
        content: const Text('This will reset all system settings to their default values. Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _enableAuditLogging = true;
        _enableAutoBackup = false;
        _enableSessionTimeout = true;
        _enablePasswordComplexity = false;
        _enableDataEncryption = true;
        _sessionTimeoutMinutes = 1440;
        _autoBackupDays = 7;
        _maxLoginAttempts = 5;
        _passwordMinLength = 6;
      });

      await _saveAllSettings();
    }
  }
}

class _NumberInputDialog extends StatefulWidget {
  final String title;
  final String label;
  final int initialValue;
  final int min;
  final int max;

  const _NumberInputDialog({
    required this.title,
    required this.label,
    required this.initialValue,
    required this.min,
    required this.max,
  });

  @override
  State<_NumberInputDialog> createState() => _NumberInputDialogState();
}

class _NumberInputDialogState extends State<_NumberInputDialog> {
  late TextEditingController _controller;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue.toString());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: TextField(
        controller: _controller,
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          labelText: widget.label,
          errorText: _errorText,
          helperText: 'Range: ${widget.min} - ${widget.max}',
        ),
        onChanged: _validateInput,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _errorText == null ? _save : null,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _validateInput(String value) {
    final number = int.tryParse(value);
    setState(() {
      if (number == null) {
        _errorText = 'Please enter a valid number';
      } else if (number < widget.min || number > widget.max) {
        _errorText = 'Value must be between ${widget.min} and ${widget.max}';
      } else {
        _errorText = null;
      }
    });
  }

  void _save() {
    final number = int.tryParse(_controller.text);
    if (number != null && _errorText == null) {
      Navigator.of(context).pop(number);
    }
  }
}
