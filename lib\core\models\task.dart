/// Task model representing a task in the system
class Task {
  final int? id;
  final String title;
  final String? description;
  final String status;
  final DateTime createdAt;
  final DateTime? dueDate;
  final DateTime? completedAt;
  final int userId;
  final bool isShared;

  const Task({
    this.id,
    required this.title,
    this.description,
    required this.status,
    required this.createdAt,
    this.dueDate,
    this.completedAt,
    required this.userId,
    this.isShared = false,
  });

  /// Create Task from database map
  factory Task.fromMap(Map<String, dynamic> map) {
    return Task(
      id: map['id'] as int?,
      title: map['title'] as String,
      description: map['description'] as String?,
      status: map['status'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      dueDate: map['due_date'] != null 
          ? DateTime.parse(map['due_date'] as String)
          : null,
      completedAt: map['completed_at'] != null 
          ? DateTime.parse(map['completed_at'] as String)
          : null,
      userId: map['user_id'] as int,
      isShared: (map['is_shared'] as int) == 1,
    );
  }

  /// Convert Task to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'user_id': userId,
      'is_shared': isShared ? 1 : 0,
    };
  }

  /// Create a copy of Task with updated fields
  Task copyWith({
    int? id,
    String? title,
    String? description,
    String? status,
    DateTime? createdAt,
    DateTime? dueDate,
    DateTime? completedAt,
    int? userId,
    bool? isShared,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      dueDate: dueDate ?? this.dueDate,
      completedAt: completedAt ?? this.completedAt,
      userId: userId ?? this.userId,
      isShared: isShared ?? this.isShared,
    );
  }

  /// Check if task is overdue
  bool get isOverdue {
    if (dueDate == null || status == 'completed') return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// Get truncated title for display
  String getTruncatedTitle(int maxLength) {
    if (title.length <= maxLength) return title;
    return '${title.substring(0, maxLength - 3)}...';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Task &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.status == status &&
        other.createdAt == createdAt &&
        other.dueDate == dueDate &&
        other.completedAt == completedAt &&
        other.userId == userId &&
        other.isShared == isShared;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      status,
      createdAt,
      dueDate,
      completedAt,
      userId,
      isShared,
    );
  }

  @override
  String toString() {
    return 'Task(id: $id, title: $title, status: $status, userId: $userId, isShared: $isShared)';
  }
}
