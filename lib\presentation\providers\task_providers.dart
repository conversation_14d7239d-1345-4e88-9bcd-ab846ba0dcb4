import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/models/task.dart';
import '../../data/services/task_service.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';

/// Provider for TaskService instance
final taskServiceProvider = Provider<TaskService>((ref) {
  return TaskService.instance;
});

/// State class for task list management
class TaskListState {
  final List<Task> tasks;
  final bool isLoading;
  final String? error;
  final String? filter;

  const TaskListState({
    this.tasks = const [],
    this.isLoading = false,
    this.error,
    this.filter,
  });

  TaskListState copyWith({
    List<Task>? tasks,
    bool? isLoading,
    String? error,
    String? filter,
  }) {
    return TaskListState(
      tasks: tasks ?? this.tasks,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filter: filter ?? this.filter,
    );
  }
}

/// Notifier for managing task list state
class TaskListNotifier extends StateNotifier<TaskListState> {
  TaskListNotifier(this._taskService) : super(const TaskListState());

  final TaskService _taskService;

  /// Load all tasks
  Future<void> loadTasks({int? userId, String? status}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      List<Task> tasks;
      if (userId != null) {
        tasks = await _taskService.getUserTasks(userId);
      } else {
        tasks = await _taskService.getAllTasks();
      }

      // Apply status filter if provided
      if (status != null) {
        tasks = tasks.where((task) => task.status == status).toList();
      }

      state = state.copyWith(tasks: tasks, isLoading: false, filter: status);

      await Logger.instance.info('Task provider: Loaded ${tasks.length} tasks');
    } catch (e) {
      await Logger.instance.error('Task provider: Failed to load tasks', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Failed to load tasks',
      );
    }
  }

  /// Create a new task
  Future<bool> createTask({
    required String title,
    String? description,
    DateTime? dueDate,
    String? status,
    String? priority,
    String? category,
    List<String>? tags,
    int userId = 1,
    bool isShared = false,
  }) async {
    try {
      await Logger.instance.info('Task provider: Creating new task - $title');

      final newTask = await _taskService.createTask(
        title: title,
        description: description,
        dueDate: dueDate,
        status: status ?? 'pending',
        priority: priority ?? 'medium',
        category: category ?? 'general',
        tags: tags ?? [],
        userId: userId,
        isShared: isShared,
      );

      // Add the new task to the current list
      final updatedTasks = [newTask, ...state.tasks];
      state = state.copyWith(tasks: updatedTasks);

      await Logger.instance.info('Task provider: Task created successfully');
      return true;
    } catch (e) {
      await Logger.instance.error('Task provider: Failed to create task', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to create task',
      );
      return false;
    }
  }

  /// Update an existing task
  Future<bool> updateTask({
    required int id,
    String? title,
    String? description,
    String? status,
    String? priority,
    String? category,
    List<String>? tags,
    DateTime? dueDate,
    bool? isShared,
  }) async {
    try {
      await Logger.instance.info('Task provider: Updating task ID - $id');

      final updatedTask = await _taskService.updateTask(
        id: id,
        title: title,
        description: description,
        status: status,
        priority: priority,
        category: category,
        tags: tags,
        dueDate: dueDate,
        isShared: isShared,
      );

      // Update the task in the current list
      final updatedTasks = state.tasks.map((task) {
        return task.id == id ? updatedTask : task;
      }).toList();

      state = state.copyWith(tasks: updatedTasks);

      await Logger.instance.info('Task provider: Task updated successfully');
      return true;
    } catch (e) {
      await Logger.instance.error('Task provider: Failed to update task', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to update task',
      );
      return false;
    }
  }

  /// Delete a task
  Future<bool> deleteTask(int id) async {
    try {
      await Logger.instance.info('Task provider: Deleting task ID - $id');

      final success = await _taskService.deleteTask(id);

      if (success) {
        // Remove the task from the current list
        final updatedTasks = state.tasks
            .where((task) => task.id != id)
            .toList();
        state = state.copyWith(tasks: updatedTasks);

        await Logger.instance.info('Task provider: Task deleted successfully');
      }

      return success;
    } catch (e) {
      await Logger.instance.error('Task provider: Failed to delete task', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to delete task',
      );
      return false;
    }
  }

  /// Mark task as completed
  Future<bool> completeTask(int id) async {
    try {
      await Logger.instance.info('Task provider: Completing task ID - $id');

      final completedTask = await _taskService.completeTask(id);

      // Update the task in the current list
      final updatedTasks = state.tasks.map((task) {
        return task.id == id ? completedTask : task;
      }).toList();

      state = state.copyWith(tasks: updatedTasks);

      await Logger.instance.info('Task provider: Task completed successfully');
      return true;
    } catch (e) {
      await Logger.instance.error('Task provider: Failed to complete task', e);
      state = state.copyWith(
        error: e is AppException ? e.message : 'Failed to complete task',
      );
      return false;
    }
  }

  /// Search tasks
  Future<void> searchTasks(String query, {int? userId}) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final tasks = await _taskService.searchTasks(query, userId: userId);

      state = state.copyWith(tasks: tasks, isLoading: false);

      await Logger.instance.info(
        'Task provider: Search completed - ${tasks.length} results',
      );
    } catch (e) {
      await Logger.instance.error('Task provider: Failed to search tasks', e);
      state = state.copyWith(
        isLoading: false,
        error: e is AppException ? e.message : 'Failed to search tasks',
      );
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh tasks
  Future<void> refresh({int? userId}) async {
    await loadTasks(userId: userId, status: state.filter);
  }
}

/// Provider for task list state management
final taskListProvider = StateNotifierProvider<TaskListNotifier, TaskListState>(
  (ref) {
    final taskService = ref.watch(taskServiceProvider);
    return TaskListNotifier(taskService);
  },
);

/// Provider for task statistics
final taskStatisticsProvider = FutureProvider.family<Map<String, int>, int?>((
  ref,
  userId,
) async {
  final taskService = ref.watch(taskServiceProvider);
  return await taskService.getTaskStatistics(userId: userId);
});

/// Provider for overdue tasks
final overdueTasksProvider = FutureProvider.family<List<Task>, int?>((
  ref,
  userId,
) async {
  final taskService = ref.watch(taskServiceProvider);
  return await taskService.getOverdueTasks(userId: userId);
});

/// Provider for getting a specific task by ID
final taskByIdProvider = FutureProvider.family<Task?, int>((ref, id) async {
  final taskService = ref.watch(taskServiceProvider);
  return await taskService.getTaskById(id);
});
