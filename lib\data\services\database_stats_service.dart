import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../repositories/task_repository.dart';
import '../repositories/user_repository.dart';

/// Service for calculating database statistics and storage usage
class DatabaseStatsService {
  static DatabaseStatsService? _instance;
  static DatabaseStatsService get instance => _instance ??= DatabaseStatsService._();
  
  DatabaseStatsService._();

  final TaskRepository _taskRepository = TaskRepository.instance;
  final UserRepository _userRepository = UserRepository.instance;

  /// Get comprehensive database statistics
  Future<Map<String, dynamic>> getDatabaseStatistics() async {
    try {
      await Logger.instance.info('Calculating database statistics...');

      // Get database file size
      final dbSize = await _getDatabaseFileSize();
      
      // Get record counts
      final taskCount = await _getTaskCount();
      final userCount = await _getUserCount();
      final activeUserCount = await _getActiveUserCount();
      
      // Get storage breakdown
      final storageBreakdown = await _getStorageBreakdown();
      
      // Calculate growth metrics
      final growthMetrics = await _getGrowthMetrics();

      final stats = {
        'database_size': {
          'total_bytes': dbSize['total_bytes'],
          'total_mb': dbSize['total_mb'],
          'formatted_size': dbSize['formatted_size'],
        },
        'record_counts': {
          'total_tasks': taskCount,
          'total_users': userCount,
          'active_users': activeUserCount,
          'inactive_users': userCount - activeUserCount,
        },
        'storage_breakdown': storageBreakdown,
        'growth_metrics': growthMetrics,
        'last_calculated': DateTime.now().toIso8601String(),
      };

      await Logger.instance.info('Database statistics calculated successfully');
      return stats;
    } catch (e) {
      await Logger.instance.error('Failed to calculate database statistics', e);
      throw AppDatabaseException('Failed to calculate database statistics: $e');
    }
  }

  /// Get database file size information
  Future<Map<String, dynamic>> _getDatabaseFileSize() async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final dbDirectory = Directory(path.join(documentsDir.path, 'TaskManager'));
      
      int totalBytes = 0;
      final files = <String, int>{};

      if (await dbDirectory.exists()) {
        await for (final entity in dbDirectory.list(recursive: true)) {
          if (entity is File) {
            final stat = await entity.stat();
            final fileName = path.basename(entity.path);
            files[fileName] = stat.size;
            totalBytes += stat.size;
          }
        }
      }

      final totalMB = totalBytes / (1024 * 1024);
      final formattedSize = _formatBytes(totalBytes);

      return {
        'total_bytes': totalBytes,
        'total_mb': totalMB,
        'formatted_size': formattedSize,
        'files': files,
      };
    } catch (e) {
      await Logger.instance.error('Failed to get database file size', e);
      return {
        'total_bytes': 0,
        'total_mb': 0.0,
        'formatted_size': '0 B',
        'files': <String, int>{},
      };
    }
  }

  /// Get total task count
  Future<int> _getTaskCount() async {
    try {
      final tasks = await _taskRepository.getAllTasks();
      return tasks.length;
    } catch (e) {
      await Logger.instance.error('Failed to get task count', e);
      return 0;
    }
  }

  /// Get total user count
  Future<int> _getUserCount() async {
    try {
      final users = await _userRepository.getAllUsers(includeInactive: true);
      return users.length;
    } catch (e) {
      await Logger.instance.error('Failed to get user count', e);
      return 0;
    }
  }

  /// Get active user count
  Future<int> _getActiveUserCount() async {
    try {
      final users = await _userRepository.getAllUsers(includeInactive: false);
      return users.length;
    } catch (e) {
      await Logger.instance.error('Failed to get active user count', e);
      return 0;
    }
  }

  /// Get storage breakdown by data type
  Future<Map<String, dynamic>> _getStorageBreakdown() async {
    try {
      // Estimate storage usage by data type
      final taskCount = await _getTaskCount();
      final userCount = await _getUserCount();
      
      // Rough estimates based on average record sizes
      const avgTaskSize = 500; // bytes per task (including text fields)
      const avgUserSize = 200; // bytes per user
      const systemOverhead = 50 * 1024; // 50KB for system tables, indexes, etc.

      final taskStorage = taskCount * avgTaskSize;
      final userStorage = userCount * avgUserSize;
      final totalEstimated = taskStorage + userStorage + systemOverhead;

      return {
        'tasks': {
          'count': taskCount,
          'estimated_bytes': taskStorage,
          'formatted_size': _formatBytes(taskStorage),
          'percentage': totalEstimated > 0 ? (taskStorage / totalEstimated * 100).round() : 0,
        },
        'users': {
          'count': userCount,
          'estimated_bytes': userStorage,
          'formatted_size': _formatBytes(userStorage),
          'percentage': totalEstimated > 0 ? (userStorage / totalEstimated * 100).round() : 0,
        },
        'system': {
          'estimated_bytes': systemOverhead,
          'formatted_size': _formatBytes(systemOverhead),
          'percentage': totalEstimated > 0 ? (systemOverhead / totalEstimated * 100).round() : 0,
        },
        'total_estimated': totalEstimated,
      };
    } catch (e) {
      await Logger.instance.error('Failed to get storage breakdown', e);
      return {
        'tasks': {'count': 0, 'estimated_bytes': 0, 'formatted_size': '0 B', 'percentage': 0},
        'users': {'count': 0, 'estimated_bytes': 0, 'formatted_size': '0 B', 'percentage': 0},
        'system': {'estimated_bytes': 0, 'formatted_size': '0 B', 'percentage': 0},
        'total_estimated': 0,
      };
    }
  }

  /// Get growth metrics (simplified version)
  Future<Map<String, dynamic>> _getGrowthMetrics() async {
    try {
      // For a real implementation, you'd track these over time
      // For now, we'll provide current snapshot data
      final taskCount = await _getTaskCount();
      final userCount = await _getUserCount();
      
      return {
        'current_period': {
          'tasks_created': taskCount,
          'users_created': userCount,
          'period': 'all_time',
        },
        'growth_rate': {
          'tasks_per_day': 0.0, // Would calculate from creation dates
          'users_per_day': 0.0, // Would calculate from creation dates
        },
        'projections': {
          'estimated_size_next_month': 0, // Would calculate based on growth rate
          'estimated_records_next_month': taskCount + userCount,
        },
      };
    } catch (e) {
      await Logger.instance.error('Failed to get growth metrics', e);
      return {
        'current_period': {'tasks_created': 0, 'users_created': 0, 'period': 'all_time'},
        'growth_rate': {'tasks_per_day': 0.0, 'users_per_day': 0.0},
        'projections': {'estimated_size_next_month': 0, 'estimated_records_next_month': 0},
      };
    }
  }

  /// Format bytes to human readable string
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get backup directory size
  Future<Map<String, dynamic>> getBackupDirectorySize() async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(documentsDir.path, 'TaskManager', 'backups'));
      
      int totalBytes = 0;
      int fileCount = 0;
      final files = <Map<String, dynamic>>[];

      if (await backupDir.exists()) {
        await for (final entity in backupDir.list()) {
          if (entity is File && entity.path.endsWith('.json')) {
            final stat = await entity.stat();
            totalBytes += stat.size;
            fileCount++;
            
            files.add({
              'name': path.basename(entity.path),
              'size': stat.size,
              'formatted_size': _formatBytes(stat.size),
              'modified': stat.modified.toIso8601String(),
            });
          }
        }
      }

      return {
        'total_bytes': totalBytes,
        'formatted_size': _formatBytes(totalBytes),
        'file_count': fileCount,
        'files': files,
      };
    } catch (e) {
      await Logger.instance.error('Failed to get backup directory size', e);
      return {
        'total_bytes': 0,
        'formatted_size': '0 B',
        'file_count': 0,
        'files': <Map<String, dynamic>>[],
      };
    }
  }

  /// Clean up old backup files (keep only last N backups)
  Future<Map<String, dynamic>> cleanupOldBackups({int keepCount = 10}) async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory(path.join(documentsDir.path, 'TaskManager', 'backups'));
      
      if (!await backupDir.exists()) {
        return {'deleted_count': 0, 'freed_bytes': 0, 'freed_size': '0 B'};
      }

      final files = <File>[];
      await for (final entity in backupDir.list()) {
        if (entity is File && entity.path.endsWith('.json')) {
          files.add(entity);
        }
      }

      // Sort by modification date (newest first)
      files.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));

      // Delete old files
      int deletedCount = 0;
      int freedBytes = 0;

      if (files.length > keepCount) {
        final filesToDelete = files.skip(keepCount);
        
        for (final file in filesToDelete) {
          final stat = await file.stat();
          freedBytes += stat.size;
          await file.delete();
          deletedCount++;
        }
      }

      await Logger.instance.info('Cleaned up $deletedCount old backup files, freed ${_formatBytes(freedBytes)}');

      return {
        'deleted_count': deletedCount,
        'freed_bytes': freedBytes,
        'freed_size': _formatBytes(freedBytes),
      };
    } catch (e) {
      await Logger.instance.error('Failed to cleanup old backups', e);
      throw AppDatabaseException('Failed to cleanup old backups: $e');
    }
  }
}
