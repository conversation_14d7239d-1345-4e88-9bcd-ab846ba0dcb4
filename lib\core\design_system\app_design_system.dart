import 'package:flutter/material.dart';

/// Industrial Design System for TaskManager Pro
/// Professional, modern design system suitable for enterprise applications
class AppDesignSystem {
  // Private constructor
  AppDesignSystem._();

  /// Color Palette - Industrial Theme
  static const _primaryBlue = Color(0xFF1E3A8A);
  static const _primaryBlueLight = Color(0xFF3B82F6);
  static const _primaryBlueDark = Color(0xFF1E40AF);

  static const _secondaryTeal = Color(0xFF0F766E);
  static const _secondaryTealLight = Color(0xFF14B8A6);
  static const _secondaryTealDark = Color(0xFF0D9488);

  static const _accentOrange = Color(0xFFEA580C);
  static const _accentOrangeLight = Color(0xFFF97316);
  static const _accentOrangeDark = Color(0xDC2626);

  static const _neutralGray50 = Color(0xFFF9FAFB);
  static const _neutralGray100 = Color(0xFFF3F4F6);
  static const _neutralGray200 = Color(0xFFE5E7EB);
  static const _neutralGray300 = Color(0xFFD1D5DB);
  static const _neutralGray400 = Color(0xFF9CA3AF);
  static const _neutralGray500 = Color(0xFF6B7280);
  static const _neutralGray600 = Color(0xFF4B5563);
  static const _neutralGray700 = Color(0xFF374151);
  static const _neutralGray800 = Color(0xFF1F2937);
  static const _neutralGray900 = Color(0xFF111827);

  static const _successGreen = Color(0xFF059669);
  static const _warningYellow = Color(0xFFD97706);
  static const _errorRed = Color(0xFFDC2626);
  static const _infoBlue = Color(0xFF2563EB);

  /// Color Schemes
  static const lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: _primaryBlue,
    onPrimary: Colors.white,
    primaryContainer: Color(0xFFDEE7FF),
    onPrimaryContainer: Color(0xFF001B3D),
    secondary: _secondaryTeal,
    onSecondary: Colors.white,
    secondaryContainer: Color(0xFFCCF7F0),
    onSecondaryContainer: Color(0xFF002019),
    tertiary: _accentOrange,
    onTertiary: Colors.white,
    tertiaryContainer: Color(0xFFFFE0CC),
    onTertiaryContainer: Color(0xFF2A1000),
    error: _errorRed,
    onError: Colors.white,
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    surface: Colors.white,
    onSurface: _neutralGray900,
    surfaceContainerHighest: _neutralGray100,
    onSurfaceVariant: _neutralGray700,
    outline: _neutralGray300,
    outlineVariant: _neutralGray200,
    shadow: Colors.black26,
    scrim: Colors.black54,
    inverseSurface: _neutralGray800,
    onInverseSurface: _neutralGray100,
    inversePrimary: Color(0xFF9BB3FF),
  );

  static const darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF9BB3FF),
    onPrimary: Color(0xFF001B3D),
    primaryContainer: Color(0xFF002C5F),
    onPrimaryContainer: Color(0xFFDEE7FF),
    secondary: Color(0xFF4FD1C7),
    onSecondary: Color(0xFF002019),
    secondaryContainer: Color(0xFF00382F),
    onSecondaryContainer: Color(0xFFCCF7F0),
    tertiary: Color(0xFFFFB59A),
    onTertiary: Color(0xFF2A1000),
    tertiaryContainer: Color(0xFF5D2F00),
    onTertiaryContainer: Color(0xFFFFE0CC),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF410002),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF0F1419),
    onSurface: Color(0xFFE3E3E3),
    surfaceContainerHighest: Color(0xFF2B2F36),
    onSurfaceVariant: Color(0xFFC4C6CF),
    outline: Color(0xFF8E9099),
    outlineVariant: Color(0xFF44474E),
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: Color(0xFFE3E3E3),
    onInverseSurface: Color(0xFF2E3036),
    inversePrimary: _primaryBlue,
  );

  /// Typography Scale
  static const _fontFamily = 'Inter';

  static const textTheme = TextTheme(
    displayLarge: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      height: 1.12,
    ),
    displayMedium: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.16,
    ),
    displaySmall: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.22,
    ),
    headlineLarge: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 32,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      height: 1.25,
    ),
    headlineMedium: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 28,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      height: 1.29,
    ),
    headlineSmall: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 24,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      height: 1.33,
    ),
    titleLarge: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 22,
      fontWeight: FontWeight.w600,
      letterSpacing: 0,
      height: 1.27,
    ),
    titleMedium: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.15,
      height: 1.50,
    ),
    titleSmall: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    bodyLarge: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      height: 1.50,
    ),
    bodyMedium: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.43,
    ),
    bodySmall: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.33,
    ),
    labelLarge: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    labelMedium: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.33,
    ),
    labelSmall: TextStyle(
      fontFamily: _fontFamily,
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.45,
    ),
  );

  /// Spacing Scale
  static const spacing = _Spacing();

  /// Border Radius
  static const borderRadius = _BorderRadius();

  /// Shadows
  static const shadows = _Shadows();

  /// Status Colors
  static const statusColors = _StatusColors();

  /// Priority Colors
  static const priorityColors = _PriorityColors();

  /// Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: lightColorScheme,
      textTheme: textTheme,
      fontFamily: _fontFamily,

      // App Bar Theme
      appBarTheme: AppBarTheme(
        centerTitle: false,
        elevation: 0,
        scrolledUnderElevation: 1,
        backgroundColor: lightColorScheme.surface,
        foregroundColor: lightColorScheme.onSurface,
        titleTextStyle: textTheme.titleLarge?.copyWith(
          color: lightColorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(color: lightColorScheme.onSurface),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shadowColor: Colors.black12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius.medium),
        ),
        margin: EdgeInsets.all(spacing.xs),
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 2,
          shadowColor: Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius.small),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: spacing.lg,
            vertical: spacing.md,
          ),
          textStyle: textTheme.labelLarge,
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: lightColorScheme.surfaceContainerHighest,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: lightColorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: lightColorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: lightColorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: lightColorScheme.error),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: spacing.md,
          vertical: spacing.md,
        ),
        labelStyle: textTheme.bodyMedium?.copyWith(
          color: lightColorScheme.onSurfaceVariant,
        ),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: lightColorScheme.surfaceContainerHighest,
        selectedColor: lightColorScheme.primaryContainer,
        labelStyle: textTheme.labelMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius.full),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: spacing.sm,
          vertical: spacing.xs,
        ),
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        elevation: 8,
        shadowColor: Colors.black26,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius.large),
        ),
        titleTextStyle: textTheme.headlineSmall?.copyWith(
          color: lightColorScheme.onSurface,
        ),
        contentTextStyle: textTheme.bodyMedium?.copyWith(
          color: lightColorScheme.onSurfaceVariant,
        ),
      ),

      // Drawer Theme
      drawerTheme: DrawerThemeData(
        backgroundColor: lightColorScheme.surface,
        elevation: 8,
        shadowColor: Colors.black26,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
        ),
      ),
    );
  }

  /// Dark Theme
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      colorScheme: darkColorScheme,
      appBarTheme: lightTheme.appBarTheme.copyWith(
        backgroundColor: darkColorScheme.surface,
        foregroundColor: darkColorScheme.onSurface,
        titleTextStyle: textTheme.titleLarge?.copyWith(
          color: darkColorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
        iconTheme: IconThemeData(color: darkColorScheme.onSurface),
      ),
      inputDecorationTheme: lightTheme.inputDecorationTheme.copyWith(
        fillColor: darkColorScheme.surfaceContainerHighest,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: darkColorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: darkColorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadius.small),
          borderSide: BorderSide(color: darkColorScheme.primary, width: 2),
        ),
        labelStyle: textTheme.bodyMedium?.copyWith(
          color: darkColorScheme.onSurfaceVariant,
        ),
      ),
      chipTheme: lightTheme.chipTheme.copyWith(
        backgroundColor: darkColorScheme.surfaceContainerHighest,
        selectedColor: darkColorScheme.primaryContainer,
      ),
      drawerTheme: lightTheme.drawerTheme.copyWith(
        backgroundColor: darkColorScheme.surface,
      ),
    );
  }
}

/// Spacing constants
class _Spacing {
  const _Spacing();

  double get xs => 4.0;
  double get sm => 8.0;
  double get md => 16.0;
  double get lg => 24.0;
  double get xl => 32.0;
  double get xxl => 48.0;
  double get xxxl => 64.0;
}

/// Border radius constants
class _BorderRadius {
  const _BorderRadius();

  double get small => 8.0;
  double get medium => 12.0;
  double get large => 16.0;
  double get xl => 24.0;
  double get full => 9999.0;
}

/// Shadow definitions
class _Shadows {
  const _Shadows();

  List<BoxShadow> get small => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05),
      blurRadius: 2,
      offset: const Offset(0, 1),
    ),
  ];

  List<BoxShadow> get medium => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: 8,
      offset: const Offset(0, 4),
    ),
  ];

  List<BoxShadow> get large => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];
}

/// Status color definitions
class _StatusColors {
  const _StatusColors();

  Color get success => AppDesignSystem._successGreen;
  Color get warning => AppDesignSystem._warningYellow;
  Color get error => AppDesignSystem._errorRed;
  Color get info => AppDesignSystem._infoBlue;
}

/// Priority color definitions
class _PriorityColors {
  const _PriorityColors();

  Color get low => const Color(0xFF10B981);
  Color get medium => const Color(0xFF3B82F6);
  Color get high => const Color(0xFFF59E0B);
  Color get critical => const Color(0xFFEF4444);
}
