import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../core/constants/app_constants.dart';
import '../../core/models/task.dart';
import '../providers/task_providers.dart';
import '../widgets/task_card.dart';
import '../widgets/app_drawer.dart';
import '../widgets/modern_add_task_modal.dart';
import 'add_edit_task_screen.dart';

/// Screen displaying the list of tasks
class TaskListScreen extends ConsumerStatefulWidget {
  const TaskListScreen({super.key});

  @override
  ConsumerState<TaskListScreen> createState() => _TaskListScreenState();
}

class _TaskListScreenState extends ConsumerState<TaskListScreen> {
  String _selectedFilter = 'all';
  String _selectedPriority = 'all';
  String _selectedCategory = 'all';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load tasks when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(taskListProvider.notifier).loadTasks();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _applyFilters();
  }

  void _onPriorityChanged(String priority) {
    setState(() {
      _selectedPriority = priority;
    });
    _applyFilters();
  }

  void _onCategoryChanged(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _applyFilters();
  }

  void _applyFilters() {
    final notifier = ref.read(taskListProvider.notifier);

    // For now, we'll just apply status filter
    // In a real app, you'd implement combined filtering
    if (_selectedFilter == 'all') {
      notifier.loadTasks();
    } else {
      notifier.loadTasks(status: _selectedFilter);
    }
  }

  void _onSearchChanged(String query) {
    if (query.isEmpty) {
      ref.read(taskListProvider.notifier).loadTasks();
    } else if (query.length >= 2) {
      ref.read(taskListProvider.notifier).searchTasks(query);
    }
  }

  Future<void> _addNewTask() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const ModernAddTaskModal(),
    );

    if (result == true) {
      // Task was created successfully, refresh the list
      ref.read(taskListProvider.notifier).loadTasks();
    }
  }

  Future<void> _editTask(Task task) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(builder: (context) => AddEditTaskScreen(task: task)),
    );

    if (result == true) {
      // Refresh the task list
      ref.read(taskListProvider.notifier).refresh();
    }
  }

  Future<void> _deleteTask(Task task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Task'),
        content: Text('Are you sure you want to delete "${task.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(taskListProvider.notifier)
          .deleteTask(task.id!);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _toggleTaskStatus(Task task) async {
    final newStatus = task.status == AppConstants.taskStatusCompleted
        ? AppConstants.taskStatusPending
        : AppConstants.taskStatusCompleted;

    final success = await ref
        .read(taskListProvider.notifier)
        .updateTask(id: task.id!, status: newStatus);

    if (success && mounted) {
      final message = newStatus == AppConstants.taskStatusCompleted
          ? 'Task completed!'
          : 'Task marked as pending';

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.green),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final taskListState = ref.watch(taskListProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Tasks'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(taskListProvider.notifier).refresh(),
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search tasks...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _onSearchChanged,
            ),
          ),

          // Filter Chips
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Status Filters
                Text('Status', style: Theme.of(context).textTheme.labelMedium),
                const SizedBox(height: 4),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'all',
                        'All',
                        _selectedFilter,
                        _onFilterChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskStatusPending,
                        'Pending',
                        _selectedFilter,
                        _onFilterChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskStatusInProgress,
                        'In Progress',
                        _selectedFilter,
                        _onFilterChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskStatusCompleted,
                        'Completed',
                        _selectedFilter,
                        _onFilterChanged,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),

                // Priority Filters
                Text(
                  'Priority',
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                const SizedBox(height: 4),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'all',
                        'All',
                        _selectedPriority,
                        _onPriorityChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskPriorityLow,
                        'Low',
                        _selectedPriority,
                        _onPriorityChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskPriorityMedium,
                        'Medium',
                        _selectedPriority,
                        _onPriorityChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskPriorityHigh,
                        'High',
                        _selectedPriority,
                        _onPriorityChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskPriorityCritical,
                        'Critical',
                        _selectedPriority,
                        _onPriorityChanged,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),

                // Category Filters
                Text(
                  'Category',
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                const SizedBox(height: 4),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'all',
                        'All',
                        _selectedCategory,
                        _onCategoryChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskCategoryWork,
                        'Work',
                        _selectedCategory,
                        _onCategoryChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskCategoryPersonal,
                        'Personal',
                        _selectedCategory,
                        _onCategoryChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskCategoryProject,
                        'Project',
                        _selectedCategory,
                        _onCategoryChanged,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      _buildFilterChip(
                        AppConstants.taskCategoryMeeting,
                        'Meeting',
                        _selectedCategory,
                        _onCategoryChanged,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Task List
          Expanded(child: _buildTaskList(taskListState)),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addNewTask,
        icon: const Icon(Icons.add),
        label: const Text('Add Task'),
      ),
    );
  }

  Widget _buildFilterChip(
    String value,
    String label,
    String currentValue,
    Function(String) onChanged,
  ) {
    return FilterChip(
      label: Text(label),
      selected: currentValue == value,
      onSelected: (selected) {
        if (selected) {
          onChanged(value);
        }
      },
    );
  }

  Widget _buildTaskList(TaskListState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'Error: ${state.error}',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            ElevatedButton(
              onPressed: () => ref.read(taskListProvider.notifier).refresh(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_alt,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'No tasks found',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'Tap the + button to create your first task',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(taskListProvider.notifier).refresh(),
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: state.tasks.length,
        itemBuilder: (context, index) {
          final task = state.tasks[index];
          return TaskCard(
            task: task,
            onTap: () => _editTask(task),
            onStatusToggle: () => _toggleTaskStatus(task),
            onDelete: () => _deleteTask(task),
          );
        },
      ),
    );
  }
}
