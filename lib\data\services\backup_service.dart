import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../core/models/task.dart';
import '../../core/models/user.dart';
import '../../core/exceptions/app_exceptions.dart';
import '../../core/utils/logger.dart';
import '../../core/constants/app_constants.dart';
import '../repositories/task_repository.dart';
import '../repositories/user_repository.dart';
import 'encryption_service.dart';

/// Service for handling backup and restore operations
class BackupService {
  static BackupService? _instance;
  static BackupService get instance => _instance ??= BackupService._();

  BackupService._();

  final TaskRepository _taskRepository = TaskRepository.instance;
  final UserRepository _userRepository = UserRepository.instance;
  final EncryptionService _encryptionService = EncryptionService.instance;

  /// Create a complete backup of the database
  Future<String> createBackup({
    bool includeUsers = true,
    bool includeTasks = true,
    String? customPath,
  }) async {
    try {
      await Logger.instance.info('Starting backup creation...');

      final backupData = <String, dynamic>{
        'metadata': {
          'version': AppConstants.appVersion,
          'created_at': DateTime.now().toIso8601String(),
          'backup_type': 'full',
          'includes_users': includeUsers,
          'includes_tasks': includeTasks,
        },
      };

      // Backup users
      if (includeUsers) {
        final users = await _userRepository.getAllUsers(includeInactive: true);
        backupData['users'] = users
            .map(
              (user) => {
                'id': user.id,
                'username': user.username,
                'password_hash': user.passwordHash,
                'role': user.role,
                'created_at': user.createdAt.toIso8601String(),
                'last_login_at': user.lastLoginAt?.toIso8601String(),
                'is_active': user.isActive,
              },
            )
            .toList();

        await Logger.instance.info('Backed up ${users.length} users');
      }

      // Backup tasks
      if (includeTasks) {
        final tasks = await _taskRepository.getAllTasks();
        backupData['tasks'] = tasks
            .map(
              (task) => {
                'id': task.id,
                'title': task.title,
                'description': task.description,
                'status': task.status,
                'priority': task.priority,
                'category': task.category,
                'tags': task.tags.join(','),
                'due_date': task.dueDate?.toIso8601String(),
                'created_at': task.createdAt.toIso8601String(),
                'completed_at': task.completedAt?.toIso8601String(),
                'user_id': task.userId,
                'is_shared': task.isShared,
              },
            )
            .toList();

        await Logger.instance.info('Backed up ${tasks.length} tasks');
      }

      // Get backup directory
      final backupDir = await _getBackupDirectory();
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final fileName = customPath ?? 'backup_$timestamp.json';
      final backupFile = File(path.join(backupDir.path, fileName));

      // Write backup data
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);
      await backupFile.writeAsString(jsonString);

      await Logger.instance.info(
        'Backup created successfully: ${backupFile.path}',
      );
      return backupFile.path;
    } catch (e) {
      await Logger.instance.error('Failed to create backup', e);
      throw BackupException('Failed to create backup: $e');
    }
  }

  /// Restore data from a backup file
  Future<void> restoreFromBackup(String backupPath) async {
    try {
      await Logger.instance.info('Starting restore from: $backupPath');

      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw BackupException('Backup file not found: $backupPath');
      }

      final jsonString = await backupFile.readAsString();
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      // Validate backup format
      if (!backupData.containsKey('metadata')) {
        throw BackupException('Invalid backup format: missing metadata');
      }

      final metadata = backupData['metadata'] as Map<String, dynamic>;
      await Logger.instance.info(
        'Restoring backup created at: ${metadata['created_at']}',
      );

      // Restore users
      if (backupData.containsKey('users')) {
        await _restoreUsers(backupData['users'] as List<dynamic>);
      }

      // Restore tasks
      if (backupData.containsKey('tasks')) {
        await _restoreTasks(backupData['tasks'] as List<dynamic>);
      }

      await Logger.instance.info('Restore completed successfully');
    } catch (e) {
      await Logger.instance.error('Failed to restore from backup', e);
      throw BackupException('Failed to restore from backup: $e');
    }
  }

  /// Get list of available backup files
  Future<List<Map<String, dynamic>>> getAvailableBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      final backups = <Map<String, dynamic>>[];

      if (await backupDir.exists()) {
        final files = backupDir
            .listSync()
            .where((entity) => entity is File && entity.path.endsWith('.json'))
            .cast<File>()
            .toList();

        for (final file in files) {
          try {
            final stat = await file.stat();
            final jsonString = await file.readAsString();
            final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

            if (backupData.containsKey('metadata')) {
              final metadata = backupData['metadata'] as Map<String, dynamic>;
              backups.add({
                'path': file.path,
                'name': path.basename(file.path),
                'size': stat.size,
                'created_at': metadata['created_at'],
                'version': metadata['version'],
                'backup_type': metadata['backup_type'],
                'includes_users': metadata['includes_users'] ?? false,
                'includes_tasks': metadata['includes_tasks'] ?? false,
              });
            }
          } catch (e) {
            await Logger.instance.warning(
              'Failed to read backup file: ${file.path}',
            );
          }
        }
      }

      backups.sort(
        (a, b) =>
            (b['created_at'] as String).compareTo(a['created_at'] as String),
      );
      return backups;
    } catch (e) {
      await Logger.instance.error('Failed to get available backups', e);
      return [];
    }
  }

  /// Delete a backup file
  Future<bool> deleteBackup(String backupPath) async {
    try {
      final file = File(backupPath);
      if (await file.exists()) {
        await file.delete();
        await Logger.instance.info('Backup deleted: $backupPath');
        return true;
      }
      return false;
    } catch (e) {
      await Logger.instance.error('Failed to delete backup', e);
      return false;
    }
  }

  /// Export backup to external location
  Future<String?> exportBackup(
    String backupPath,
    String destinationPath,
  ) async {
    try {
      final sourceFile = File(backupPath);

      if (!await sourceFile.exists()) {
        throw BackupException('Source backup file not found');
      }

      await sourceFile.copy(destinationPath);
      await Logger.instance.info('Backup exported to: $destinationPath');
      return destinationPath;
    } catch (e) {
      await Logger.instance.error('Failed to export backup', e);
      return null;
    }
  }

  /// Import backup from external location
  Future<String?> importBackup(String sourcePath) async {
    try {
      final sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw BackupException('Source file not found');
      }

      // Validate backup format
      final jsonString = await sourceFile.readAsString();
      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

      if (!backupData.containsKey('metadata')) {
        throw BackupException('Invalid backup format');
      }

      final backupDir = await _getBackupDirectory();
      final fileName = 'imported_${path.basename(sourcePath)}';
      final destinationFile = File(path.join(backupDir.path, fileName));

      await sourceFile.copy(destinationFile.path);
      await Logger.instance.info('Backup imported: ${destinationFile.path}');
      return destinationFile.path;
    } catch (e) {
      await Logger.instance.error('Failed to import backup', e);
      return null;
    }
  }

  /// Get backup directory
  Future<Directory> _getBackupDirectory() async {
    final documentsDir = await getApplicationDocumentsDirectory();
    final backupDir = Directory(
      path.join(documentsDir.path, 'TaskManager', 'backups'),
    );

    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }

    return backupDir;
  }

  /// Restore users from backup data
  Future<void> _restoreUsers(List<dynamic> usersData) async {
    try {
      await Logger.instance.info('Restoring ${usersData.length} users...');

      for (final userData in usersData) {
        final userMap = userData as Map<String, dynamic>;

        // Skip admin user to prevent conflicts
        if (userMap['username'] == AppConstants.adminUsername) {
          continue;
        }

        final user = User(
          id: userMap['id'] as int?,
          username: userMap['username'] as String,
          passwordHash: userMap['password_hash'] as String,
          role: userMap['role'] as String,
          createdAt: DateTime.parse(userMap['created_at'] as String),
          lastLoginAt: userMap['last_login_at'] != null
              ? DateTime.parse(userMap['last_login_at'] as String)
              : null,
          isActive: userMap['is_active'] as bool,
        );

        // Check if user already exists
        final existingUser = await _userRepository.getUserByUsername(
          user.username,
        );
        if (existingUser == null) {
          await _userRepository.createUser(user);
        } else {
          await Logger.instance.info(
            'User ${user.username} already exists, skipping',
          );
        }
      }

      await Logger.instance.info('Users restored successfully');
    } catch (e) {
      await Logger.instance.error('Failed to restore users', e);
      throw BackupException('Failed to restore users: $e');
    }
  }

  /// Restore tasks from backup data
  Future<void> _restoreTasks(List<dynamic> tasksData) async {
    try {
      await Logger.instance.info('Restoring ${tasksData.length} tasks...');

      for (final taskData in tasksData) {
        final taskMap = taskData as Map<String, dynamic>;

        final task = Task(
          id: taskMap['id'] as int?,
          title: taskMap['title'] as String,
          description: taskMap['description'] as String?,
          status: taskMap['status'] as String,
          priority: taskMap['priority'] as String? ?? 'medium',
          category: taskMap['category'] as String? ?? 'general',
          tags: taskMap['tags'] != null
              ? (taskMap['tags'] as String)
                    .split(',')
                    .where((tag) => tag.isNotEmpty)
                    .toList()
              : [],
          dueDate: taskMap['due_date'] != null
              ? DateTime.parse(taskMap['due_date'] as String)
              : null,
          createdAt: DateTime.parse(taskMap['created_at'] as String),
          completedAt: taskMap['completed_at'] != null
              ? DateTime.parse(taskMap['completed_at'] as String)
              : null,
          userId: (taskMap['user_id'] as int?) ?? 1,
          isShared: (taskMap['is_shared'] as bool?) ?? false,
        );

        await _taskRepository.createTask(task);
      }

      await Logger.instance.info('Tasks restored successfully');
    } catch (e) {
      await Logger.instance.error('Failed to restore tasks', e);
      throw BackupException('Failed to restore tasks: $e');
    }
  }
}
