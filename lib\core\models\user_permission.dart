/// User permission model representing permissions for a user
class UserPermission {
  final int? id;
  final int userId;
  final String permission;
  final bool isGranted;
  final DateTime createdAt;

  const UserPermission({
    this.id,
    required this.userId,
    required this.permission,
    required this.isGranted,
    required this.createdAt,
  });

  /// Create UserPermission from database map
  factory UserPermission.fromMap(Map<String, dynamic> map) {
    return UserPermission(
      id: map['id'] as int?,
      userId: map['user_id'] as int,
      permission: map['permission'] as String,
      isGranted: (map['is_granted'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }

  /// Convert UserPermission to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'permission': permission,
      'is_granted': isGranted ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Create a copy of User<PERSON>ermission with updated fields
  UserPermission copyWith({
    int? id,
    int? userId,
    String? permission,
    bool? isGranted,
    DateTime? createdAt,
  }) {
    return UserPermission(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      permission: permission ?? this.permission,
      isGranted: isGranted ?? this.isGranted,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserPermission &&
        other.id == id &&
        other.userId == userId &&
        other.permission == permission &&
        other.isGranted == isGranted &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      userId,
      permission,
      isGranted,
      createdAt,
    );
  }

  @override
  String toString() {
    return 'UserPermission(id: $id, userId: $userId, permission: $permission, isGranted: $isGranted)';
  }
}

/// Helper class to manage user permissions
class UserPermissions {
  final List<UserPermission> permissions;

  const UserPermissions(this.permissions);

  /// Check if user has a specific permission
  bool hasPermission(String permission) {
    return permissions.any(
      (p) => p.permission == permission && p.isGranted,
    );
  }

  /// Get all granted permissions
  List<String> get grantedPermissions {
    return permissions
        .where((p) => p.isGranted)
        .map((p) => p.permission)
        .toList();
  }

  /// Check if user can create tasks
  bool get canCreateTasks => hasPermission('create_task');

  /// Check if user can edit tasks
  bool get canEditTasks => hasPermission('edit_task');

  /// Check if user can delete tasks
  bool get canDeleteTasks => hasPermission('delete_task');

  /// Check if user can view shared tasks
  bool get canViewSharedTasks => hasPermission('view_shared_tasks');
}
